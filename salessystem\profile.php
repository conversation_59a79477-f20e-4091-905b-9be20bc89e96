<?php
/**
 * صفحة الملف الشخصي للمستخدم
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$user_data = null;
$update_success = false;
$errors = [];

// الحصول على بيانات المستخدم الحالي
try {
    $stmt = $main_db->prepare("SELECT id, username, full_name, email, created_at FROM users WHERE id = ?");
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $user_data = $result->fetch_assoc();
    $stmt->close();
} catch (Exception $e) {
    $errors[] = "خطأ في جلب بيانات المستخدم: " . $e->getMessage();
}

// معالجة تحديث البيانات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // التحقق من صحة البيانات
    if (empty($full_name)) {
        $errors[] = "الاسم الكامل مطلوب";
    }
    
    if (empty($email)) {
        $errors[] = "البريد الإلكتروني مطلوب";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "البريد الإلكتروني غير صحيح";
    }
    
    // التحقق من عدم وجود بريد إلكتروني مكرر
    if (empty($errors)) {
        $stmt = $main_db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
        $stmt->bind_param("si", $email, $_SESSION['user_id']);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $errors[] = "البريد الإلكتروني موجود بالفعل لمستخدم آخر";
        }
        $stmt->close();
    }
    
    // التحقق من كلمة المرور إذا تم إدخالها
    $update_password = false;
    if (!empty($new_password)) {
        if (empty($current_password)) {
            $errors[] = "يجب إدخال كلمة المرور الحالية لتغيير كلمة المرور";
        } else {
            // التحقق من كلمة المرور الحالية
            $stmt = $main_db->prepare("SELECT password FROM users WHERE id = ?");
            $stmt->bind_param("i", $_SESSION['user_id']);
            $stmt->execute();
            $result = $stmt->get_result();
            $user_password_data = $result->fetch_assoc();
            $stmt->close();
            
            if (!password_verify($current_password, $user_password_data['password'])) {
                $errors[] = "كلمة المرور الحالية غير صحيحة";
            } elseif (strlen($new_password) < 6) {
                $errors[] = "كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل";
            } elseif ($new_password !== $confirm_password) {
                $errors[] = "كلمة المرور الجديدة وتأكيدها غير متطابقتين";
            } else {
                $update_password = true;
            }
        }
    }
    
    // تحديث البيانات إذا لم توجد أخطاء
    if (empty($errors)) {
        try {
            if ($update_password) {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $main_db->prepare("UPDATE users SET full_name = ?, email = ?, password = ? WHERE id = ?");
                $stmt->bind_param("sssi", $full_name, $email, $hashed_password, $_SESSION['user_id']);
            } else {
                $stmt = $main_db->prepare("UPDATE users SET full_name = ?, email = ? WHERE id = ?");
                $stmt->bind_param("ssi", $full_name, $email, $_SESSION['user_id']);
            }
            
            if ($stmt->execute()) {
                $update_success = true;
                $_SESSION['success'] = "تم تحديث الملف الشخصي بنجاح";
                
                // تحديث بيانات المستخدم المعروضة
                $user_data['full_name'] = $full_name;
                $user_data['email'] = $email;
                
                if ($update_password) {
                    $_SESSION['success'] .= " وتم تغيير كلمة المرور";
                }
            } else {
                $errors[] = "حدث خطأ أثناء تحديث البيانات";
            }
            $stmt->close();
        } catch (Exception $e) {
            $errors[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
        }
    }
    
    if (!empty($errors)) {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

displayMessages();
?>

<!-- تضمين ملف CSS الخاص بالملف الشخصي -->
<link rel="stylesheet" href="assets/css/profile.css">

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card profile-card fade-in">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-circle"></i>
                        الملف الشخصي
                    </h4>
                </div>
                <div class="card-body">
                    <?php if ($user_data): ?>
                    
                    <!-- معلومات المستخدم الأساسية -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="text-center">
                                <div class="avatar-placeholder bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" 
                                     style="width: 120px; height: 120px; font-size: 48px;">
                                    <i class="fas fa-user"></i>
                                </div>
                                <h5 class="mt-3"><?php echo htmlspecialchars($user_data['full_name']); ?></h5>
                                <p class="text-muted">@<?php echo htmlspecialchars($user_data['username']); ?></p>
                                <small class="text-muted">
                                    عضو منذ: <?php echo date('d/m/Y', strtotime($user_data['created_at'])); ?>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h5>معلومات الحساب</h5>
                            <table class="table table-borderless info-table">
                                <tr>
                                    <td><strong>اسم المستخدم:</strong></td>
                                    <td><?php echo htmlspecialchars($user_data['username']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>الاسم الكامل:</strong></td>
                                    <td><?php echo htmlspecialchars($user_data['full_name']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>البريد الإلكتروني:</strong></td>
                                    <td><?php echo htmlspecialchars($user_data['email']); ?></td>
                                </tr>
                                <tr>
                                    <td><strong>تاريخ التسجيل:</strong></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($user_data['created_at'])); ?></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- نموذج تحديث البيانات -->
                    <h5 class="mb-3">
                        <i class="fas fa-edit"></i>
                        تحديث البيانات
                    </h5>
                    
                    <form method="POST" id="profileForm" class="update-form">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username_display" class="form-label">اسم المستخدم</label>
                                    <input type="text" class="form-control" id="username_display" 
                                           value="<?php echo htmlspecialchars($user_data['username']); ?>" 
                                           disabled>
                                    <small class="form-text text-muted">اسم المستخدم غير قابل للتعديل</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($user_data['full_name']); ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?php echo htmlspecialchars($user_data['email']); ?>" required>
                        </div>
                        
                        <hr>
                        
                        <div class="password-section">
                            <h6 class="mb-3">
                                <i class="fas fa-key"></i>
                                تغيير كلمة المرور (اختياري)
                            </h6>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">كلمة المرور الحالية</label>
                                    <input type="password" class="form-control" id="current_password" name="current_password">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" id="new_password" name="new_password" minlength="6">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">تأكيد كلمة المرور</label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" minlength="6">
                                </div>
                            </div>
                        </div>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>ملاحظة:</strong> إذا كنت تريد تغيير كلمة المرور، يجب ملء جميع حقول كلمة المرور. 
                            إذا كنت لا تريد تغييرها، اتركها فارغة.
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-secondary btn-profile me-md-2" onclick="resetForm()">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>
                            <button type="submit" name="update_profile" class="btn btn-primary btn-profile">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                    
                    <?php else: ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        لا يمكن جلب بيانات المستخدم. يرجى المحاولة مرة أخرى.
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- روابط إضافية -->
            <div class="card mt-4">
                <div class="card-header bg-secondary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-cogs"></i>
                        إعدادات إضافية
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إدارة الحساب:</h6>
                            <div class="d-grid gap-2">
                                <a href="index.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-home"></i> الصفحة الرئيسية
                                </a>
                                <a href="logout.php" class="btn btn-outline-danger btn-sm">
                                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>إحصائيات سريعة:</h6>
                            <?php
                            $db = getCurrentUserDB();
                            if ($db && !$db->connect_error) {
                                try {
                                    $customers_count = $db->query("SELECT COUNT(*) as count FROM customers")->fetch_assoc()['count'] ?? 0;
                                    $sales_count = $db->query("SELECT COUNT(*) as count FROM sales")->fetch_assoc()['count'] ?? 0;
                                    $purchases_count = $db->query("SELECT COUNT(*) as count FROM purchases")->fetch_assoc()['count'] ?? 0;
                                    ?>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-users text-primary"></i> العملاء: <?php echo $customers_count; ?></li>
                                        <li><i class="fas fa-shopping-cart text-success"></i> المبيعات: <?php echo $sales_count; ?></li>
                                        <li><i class="fas fa-truck text-info"></i> المشتريات: <?php echo $purchases_count; ?></li>
                                    </ul>
                                    <?php
                                } catch (Exception $e) {
                                    echo '<p class="text-muted">لا يمكن جلب الإحصائيات</p>';
                                }
                            } else {
                                echo '<p class="text-muted">لا يمكن الاتصال بقاعدة البيانات</p>';
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// دالة لإعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟ سيتم فقدان جميع التغييرات غير المحفوظة.')) {
        document.getElementById('profileForm').reset();
        // إعادة تعيين القيم الأصلية
        document.getElementById('full_name').value = '<?php echo addslashes($user_data['full_name'] ?? ''); ?>';
        document.getElementById('email').value = '<?php echo addslashes($user_data['email'] ?? ''); ?>';
    }
}

// التحقق من تطابق كلمات المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword && confirmPassword && newPassword !== confirmPassword) {
        this.setCustomValidity('كلمة المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// التحقق من ملء كلمة المرور الحالية عند إدخال كلمة مرور جديدة
document.getElementById('new_password').addEventListener('input', function() {
    const currentPassword = document.getElementById('current_password');
    if (this.value && !currentPassword.value) {
        currentPassword.setCustomValidity('يجب إدخال كلمة المرور الحالية');
    } else {
        currentPassword.setCustomValidity('');
    }
});

document.getElementById('current_password').addEventListener('input', function() {
    this.setCustomValidity('');
});
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
