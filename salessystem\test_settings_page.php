<?php
/**
 * ملف لاختبار صفحة الإعدادات
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

$test_results = [];
$errors = [];

// اختبار 1: فحص وجود ملفات الإعدادات
$files_to_check = [
    'settings.php' => 'صفحة الإعدادات الرئيسية',
    'create_settings_table.php' => 'أداة إنشاء جدول الإعدادات',
    'assets/css/settings.css' => 'ملف تنسيقات الإعدادات'
];

$test_results['files'] = [];
foreach ($files_to_check as $file => $description) {
    $file_path = __DIR__ . '/' . $file;
    $test_results['files'][$file] = [
        'exists' => file_exists($file_path),
        'description' => $description
    ];
}

// اختبار 2: فحص جدول الإعدادات
try {
    $check_table = $db->query("SHOW TABLES LIKE 'settings'");
    $test_results['table_exists'] = ($check_table && $check_table->num_rows > 0);
    
    if ($test_results['table_exists']) {
        // فحص بنية الجدول
        $structure = $db->query("DESCRIBE settings");
        $columns = [];
        while ($row = $structure->fetch_assoc()) {
            $columns[$row['Field']] = $row;
        }
        
        $required_columns = ['id', 'setting_key', 'setting_value', 'setting_type', 'category', 'description'];
        $test_results['table_structure'] = [];
        foreach ($required_columns as $column) {
            $test_results['table_structure'][$column] = isset($columns[$column]);
        }
        
        // فحص عدد الإعدادات
        $count_result = $db->query("SELECT COUNT(*) as count FROM settings");
        $test_results['settings_count'] = $count_result->fetch_assoc()['count'];
        
        // فحص الفئات
        $categories_result = $db->query("SELECT DISTINCT category FROM settings");
        $test_results['categories'] = [];
        while ($row = $categories_result->fetch_assoc()) {
            $test_results['categories'][] = $row['category'];
        }
    } else {
        $test_results['table_structure'] = [];
        $test_results['settings_count'] = 0;
        $test_results['categories'] = [];
    }
} catch (Exception $e) {
    $errors[] = "خطأ في فحص جدول الإعدادات: " . $e->getMessage();
    $test_results['table_exists'] = false;
    $test_results['table_structure'] = [];
    $test_results['settings_count'] = 0;
    $test_results['categories'] = [];
}

// اختبار 3: فحص الترجمات
$ar_translations = require __DIR__ . '/languages/ar/lang.php';
$en_translations = require __DIR__ . '/languages/en/lang.php';

$required_translations = [
    'settings', 'general_settings', 'company_settings', 'tax_settings',
    'company_name', 'company_address', 'company_phone', 'company_email',
    'default_tax_rate', 'currency_symbol', 'settings_saved'
];

$test_results['translations'] = [];
foreach ($required_translations as $key) {
    $test_results['translations'][$key] = [
        'ar' => isset($ar_translations[$key]),
        'en' => isset($en_translations[$key])
    ];
}

// اختبار 4: فحص محتوى ملف الإعدادات
if ($test_results['files']['settings.php']['exists']) {
    $settings_content = file_get_contents(__DIR__ . '/settings.php');
    $test_results['settings_features'] = [
        'has_tabs' => strpos($settings_content, 'nav-tabs') !== false,
        'has_form' => strpos($settings_content, 'settingsForm') !== false,
        'has_validation' => strpos($settings_content, 'addEventListener') !== false,
        'has_css_link' => strpos($settings_content, 'settings.css') !== false,
        'has_save_function' => strpos($settings_content, 'saveSetting') !== false
    ];
} else {
    $test_results['settings_features'] = [
        'has_tabs' => false,
        'has_form' => false,
        'has_validation' => false,
        'has_css_link' => false,
        'has_save_function' => false
    ];
}

// حساب النتيجة الإجمالية
$total_score = 0;
$max_score = 0;

// نقاط الملفات (30 نقطة)
$files_score = 0;
foreach ($test_results['files'] as $file_data) {
    if ($file_data['exists']) $files_score += 10;
}
$total_score += $files_score;
$max_score += 30;

// نقاط الجدول (25 نقطة)
$table_score = 0;
if ($test_results['table_exists']) $table_score += 10;
if (count(array_filter($test_results['table_structure'])) >= 5) $table_score += 10;
if ($test_results['settings_count'] >= 20) $table_score += 5;
$total_score += $table_score;
$max_score += 25;

// نقاط الترجمات (20 نقطة)
$translations_score = 0;
foreach ($test_results['translations'] as $translation) {
    if ($translation['ar'] && $translation['en']) {
        $translations_score += 20 / count($required_translations);
    }
}
$total_score += $translations_score;
$max_score += 20;

// نقاط الميزات (25 نقطة)
$features_score = 0;
foreach ($test_results['settings_features'] as $feature) {
    if ($feature) $features_score += 5;
}
$total_score += $features_score;
$max_score += 25;

$overall_percentage = ($total_score / $max_score) * 100;

displayMessages();
?>

<div class="container mt-4">
    <h2>اختبار صفحة الإعدادات</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات الاختبار</h5>
        <p>هذا الاختبار يتحقق من جاهزية صفحة الإعدادات:</p>
        <ul>
            <li>وجود الملفات المطلوبة</li>
            <li>جدول الإعدادات وبنيته</li>
            <li>الترجمات والميزات</li>
            <li>التنسيقات والوظائف</li>
        </ul>
    </div>
    
    <!-- النتيجة الإجمالية -->
    <div class="card mb-4">
        <div class="card-header <?php echo $overall_percentage >= 90 ? 'bg-success' : ($overall_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?> text-white">
            <h4 class="mb-0">
                <i class="fas fa-chart-pie"></i>
                النتيجة الإجمالية: <?php echo number_format($overall_percentage, 1); ?>%
            </h4>
        </div>
        <div class="card-body">
            <div class="progress mb-3" style="height: 30px;">
                <div class="progress-bar <?php echo $overall_percentage >= 90 ? 'bg-success' : ($overall_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?>" 
                     style="width: <?php echo $overall_percentage; ?>%">
                    <?php echo number_format($total_score, 1); ?>/<?php echo $max_score; ?> (<?php echo number_format($overall_percentage, 1); ?>%)
                </div>
            </div>
            
            <?php if ($overall_percentage >= 90): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> ممتاز! صفحة الإعدادات جاهزة للاستخدام
                </div>
            <?php elseif ($overall_percentage >= 70): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> جيد! الصفحة تعمل مع بعض المشاكل البسيطة
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i> يحتاج إلى إصلاح! هناك مشاكل تحتاج إلى حل
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (!empty($errors)): ?>
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">
                <i class="fas fa-times-circle"></i>
                أخطاء حدثت أثناء الاختبار
            </h5>
        </div>
        <div class="card-body">
            <ul class="list-group">
                <?php foreach ($errors as $error): ?>
                <li class="list-group-item list-group-item-danger">
                    <i class="fas fa-times text-danger"></i> <?php echo htmlspecialchars($error); ?>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="row">
        <!-- اختبار الملفات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header <?php echo $files_score >= 25 ? 'bg-success' : ($files_score >= 15 ? 'bg-warning' : 'bg-danger'); ?> text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-code"></i>
                        الملفات (<?php echo $files_score; ?>/30)
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results['files'] as $file => $data): ?>
                    <div class="d-flex justify-content-between mb-2">
                        <span><?php echo $data['description']; ?>:</span>
                        <span class="badge <?php echo $data['exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $data['exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- اختبار الجدول -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header <?php echo $table_score >= 20 ? 'bg-success' : ($table_score >= 10 ? 'bg-warning' : 'bg-danger'); ?> text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-database"></i>
                        جدول الإعدادات (<?php echo $table_score; ?>/25)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>الجدول موجود:</span>
                        <span class="badge <?php echo $test_results['table_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['table_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <?php if ($test_results['table_exists']): ?>
                    <div class="d-flex justify-content-between mb-2">
                        <span>عدد الإعدادات:</span>
                        <span class="badge bg-info"><?php echo $test_results['settings_count']; ?></span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>عدد الفئات:</span>
                        <span class="badge bg-info"><?php echo count($test_results['categories']); ?></span>
                    </div>
                    
                    <h6 class="mt-3">بنية الجدول:</h6>
                    <?php foreach ($test_results['table_structure'] as $column => $exists): ?>
                    <div class="d-flex justify-content-between">
                        <small><?php echo $column; ?>:</small>
                        <span class="badge <?php echo $exists ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $exists ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- اختبار الترجمات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-language"></i>
                        الترجمات (<?php echo number_format($translations_score, 1); ?>/20)
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach (array_slice($test_results['translations'], 0, 6) as $key => $translation): ?>
                    <div class="mb-2">
                        <strong><?php echo $key; ?>:</strong>
                        <div class="d-flex justify-content-between">
                            <span>عربي:</span>
                            <span class="badge <?php echo $translation['ar'] ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $translation['ar'] ? '✓' : '✗'; ?>
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>إنجليزي:</span>
                            <span class="badge <?php echo $translation['en'] ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $translation['en'] ? '✓' : '✗'; ?>
                            </span>
                        </div>
                    </div>
                    <hr>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
        
        <!-- اختبار الميزات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs"></i>
                        الميزات (<?php echo $features_score; ?>/25)
                    </h5>
                </div>
                <div class="card-body">
                    <?php 
                    $feature_names = [
                        'has_tabs' => 'التبويبات',
                        'has_form' => 'النموذج',
                        'has_validation' => 'التحقق',
                        'has_css_link' => 'ملف CSS',
                        'has_save_function' => 'دالة الحفظ'
                    ];
                    ?>
                    <?php foreach ($test_results['settings_features'] as $feature => $exists): ?>
                    <div class="d-flex justify-content-between mb-2">
                        <span><?php echo $feature_names[$feature] ?? $feature; ?>:</span>
                        <span class="badge <?php echo $exists ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $exists ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- معاينة الفئات -->
    <?php if (!empty($test_results['categories'])): ?>
    <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">
                <i class="fas fa-tags"></i>
                فئات الإعدادات المتاحة
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <?php foreach ($test_results['categories'] as $category): ?>
                <div class="col-md-2 mb-2">
                    <span class="badge bg-primary p-2"><?php echo htmlspecialchars($category); ?></span>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- الإجراءات -->
    <div class="card">
        <div class="card-header bg-dark text-white">
            <h5 class="mb-0">الإجراءات والأدوات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <h6>إدارة الإعدادات:</h6>
                    <div class="d-grid gap-2">
                        <?php if ($test_results['table_exists']): ?>
                        <a href="settings.php" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-cogs"></i> فتح صفحة الإعدادات
                        </a>
                        <?php else: ?>
                        <a href="create_settings_table.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-plus"></i> إنشاء جدول الإعدادات
                        </a>
                        <?php endif; ?>
                        
                        <a href="test_settings_page.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-redo"></i> إعادة الاختبار
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>اختبارات أخرى:</h6>
                    <div class="d-grid gap-2">
                        <a href="test_profile_page.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-user-check"></i> اختبار الملف الشخصي
                        </a>
                        <a href="test_all_fixes.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-clipboard-check"></i> الاختبار الشامل
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>العودة:</h6>
                    <div class="d-grid gap-2">
                        <a href="index.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
