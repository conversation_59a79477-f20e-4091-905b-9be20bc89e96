<?php
/**
 * ملف للتحقق من وجود الجداول المطلوبة وإنشائها إذا لم تكن موجودة
 */

// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

// التحقق من وجود جدول المشتريات
$check_purchases = $db->query("SHOW TABLES LIKE 'purchases'");
$purchases_table_exists = ($check_purchases && $check_purchases->num_rows > 0);

if (!$purchases_table_exists) {
    // إنشاء جدول المشتريات إذا لم يكن موجودًا
    $create_purchases = "CREATE TABLE `purchases` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `invoice_number` varchar(50) NOT NULL,
        `date` date NOT NULL,
        `customer_id` int(11) DEFAULT NULL,
        `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `notes` text DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

    if ($db->query($create_purchases)) {
        $_SESSION['success'] = "تم إنشاء جدول المشتريات بنجاح";
    } else {
        $_SESSION['error'] = "فشل في إنشاء جدول المشتريات: " . $db->error;
    }
} else {
    // التحقق من وجود الأعمدة المطلوبة في جدول المشتريات
    $required_columns = [
        'customer_id' => "ALTER TABLE `purchases` ADD `customer_id` int(11) DEFAULT NULL AFTER `date`",
        'subtotal' => "ALTER TABLE `purchases` ADD `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `customer_id`",
        'tax_amount' => "ALTER TABLE `purchases` ADD `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `subtotal`",
        'total_amount' => "ALTER TABLE `purchases` ADD `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `tax_amount`"
    ];

    $columns_added = false;

    foreach ($required_columns as $column => $alter_sql) {
        $check_column = $db->query("SHOW COLUMNS FROM `purchases` LIKE '$column'");
        if ($check_column && $check_column->num_rows == 0) {
            // إضافة العمود إذا لم يكن موجودًا
            if ($db->query($alter_sql)) {
                $_SESSION['success'] = isset($_SESSION['success']) ? $_SESSION['success'] . "<br>تم إضافة عمود $column إلى جدول المشتريات" : "تم إضافة عمود $column إلى جدول المشتريات";
                $columns_added = true;
            } else {
                $_SESSION['error'] = isset($_SESSION['error']) ? $_SESSION['error'] . "<br>فشل في إضافة عمود $column إلى جدول المشتريات: " . $db->error : "فشل في إضافة عمود $column إلى جدول المشتريات: " . $db->error;
            }
        }
    }

    if (!$columns_added && !isset($_SESSION['success']) && !isset($_SESSION['error'])) {
        $_SESSION['success'] = "جدول المشتريات موجود ويحتوي على جميع الأعمدة المطلوبة";
    }
}

// التحقق من وجود جدول عناصر المشتريات
$check_purchase_items = $db->query("SHOW TABLES LIKE 'purchase_items'");
if ($check_purchase_items && $check_purchase_items->num_rows == 0) {
    // إنشاء جدول عناصر المشتريات إذا لم يكن موجودًا
    $create_purchase_items = "CREATE TABLE `purchase_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `purchase_id` int(11) NOT NULL,
        `product_id` int(11) NOT NULL,
        `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        PRIMARY KEY (`id`),
        KEY `purchase_id` (`purchase_id`),
        KEY `product_id` (`product_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

    if ($db->query($create_purchase_items)) {
        $_SESSION['success'] .= "<br>تم إنشاء جدول عناصر المشتريات بنجاح";
    } else {
        $_SESSION['error'] .= "<br>فشل في إنشاء جدول عناصر المشتريات: " . $db->error;
    }
}

// التحقق من وجود جدول المبيعات
$check_sales = $db->query("SHOW TABLES LIKE 'sales'");
if ($check_sales && $check_sales->num_rows == 0) {
    // إنشاء جدول المبيعات إذا لم يكن موجودًا
    $create_sales = "CREATE TABLE `sales` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `invoice_number` varchar(50) NOT NULL,
        `date` date NOT NULL,
        `customer_id` int(11) DEFAULT NULL,
        `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `notes` text DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

    if ($db->query($create_sales)) {
        $_SESSION['success'] .= "<br>تم إنشاء جدول المبيعات بنجاح";
    } else {
        $_SESSION['error'] .= "<br>فشل في إنشاء جدول المبيعات: " . $db->error;
    }
}

// التحقق من وجود جدول عناصر المبيعات
$check_sale_items = $db->query("SHOW TABLES LIKE 'sale_items'");
if ($check_sale_items && $check_sale_items->num_rows == 0) {
    // إنشاء جدول عناصر المبيعات إذا لم يكن موجودًا
    $create_sale_items = "CREATE TABLE `sale_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `sale_id` int(11) NOT NULL,
        `product_id` int(11) NOT NULL,
        `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        PRIMARY KEY (`id`),
        KEY `sale_id` (`sale_id`),
        KEY `product_id` (`product_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

    if ($db->query($create_sale_items)) {
        $_SESSION['success'] .= "<br>تم إنشاء جدول عناصر المبيعات بنجاح";
    } else {
        $_SESSION['error'] .= "<br>فشل في إنشاء جدول عناصر المبيعات: " . $db->error;
    }
}

// التحقق من وجود جدول المنتجات
$check_products = $db->query("SHOW TABLES LIKE 'products'");
if ($check_products && $check_products->num_rows == 0) {
    // إنشاء جدول المنتجات إذا لم يكن موجودًا
    $create_products = "CREATE TABLE `products` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

    if ($db->query($create_products)) {
        $_SESSION['success'] .= "<br>تم إنشاء جدول المنتجات بنجاح";
    } else {
        $_SESSION['error'] .= "<br>فشل في إنشاء جدول المنتجات: " . $db->error;
    }
}

// التحقق من وجود جدول العملاء
$check_customers = $db->query("SHOW TABLES LIKE 'customers'");
if ($check_customers && $check_customers->num_rows == 0) {
    // إنشاء جدول العملاء إذا لم يكن موجودًا
    $create_customers = "CREATE TABLE `customers` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `name` varchar(255) NOT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `email` varchar(255) DEFAULT NULL,
        `address` text DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

    if ($db->query($create_customers)) {
        $_SESSION['success'] .= "<br>تم إنشاء جدول العملاء بنجاح";
    } else {
        $_SESSION['error'] .= "<br>فشل في إنشاء جدول العملاء: " . $db->error;
    }
}

// إضافة واجهة لعرض النتائج
require_once __DIR__ . '/includes/header.php';
displayMessages();

echo '<div class="container mt-4">';
echo '<h2>فحص الجداول</h2>';

// عرض حالة الجداول
$tables_to_check = ['purchases', 'purchase_items', 'sales', 'sale_items', 'products', 'customers'];

echo '<div class="row">';
foreach ($tables_to_check as $table) {
    $check_table = $db->query("SHOW TABLES LIKE '$table'");
    $table_exists = ($check_table && $check_table->num_rows > 0);

    echo '<div class="col-md-4 mb-3">';
    echo '<div class="card">';
    echo '<div class="card-header ' . ($table_exists ? 'bg-success text-white' : 'bg-danger text-white') . '">';
    echo '<h5 class="mb-0">جدول ' . $table . '</h5>';
    echo '</div>';
    echo '<div class="card-body">';

    if ($table_exists) {
        echo '<p class="text-success"><i class="fas fa-check-circle"></i> موجود</p>';

        // عرض عدد السجلات
        $count_result = $db->query("SELECT COUNT(*) as count FROM $table");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo '<p>عدد السجلات: <strong>' . $count . '</strong></p>';
        }

        // عرض بنية الجدول للجداول المهمة
        if (in_array($table, ['purchases', 'sales'])) {
            $structure = $db->query("DESCRIBE $table");
            echo '<h6>الأعمدة:</h6>';
            echo '<ul class="small">';
            while ($column = $structure->fetch_assoc()) {
                echo '<li>' . $column['Field'] . ' (' . $column['Type'] . ')</li>';
            }
            echo '</ul>';
        }
    } else {
        echo '<p class="text-danger"><i class="fas fa-times-circle"></i> غير موجود</p>';
    }

    echo '</div>';
    echo '</div>';
    echo '</div>';
}
echo '</div>';

// إضافة أزرار للإجراءات
echo '<div class="mt-4">';
echo '<a href="index.php" class="btn btn-primary">العودة للصفحة الرئيسية</a>';
echo '<a href="check_tables.php" class="btn btn-secondary ms-2">إعادة فحص الجداول</a>';
echo '</div>';

echo '</div>';

require_once __DIR__ . '/includes/footer.php';
