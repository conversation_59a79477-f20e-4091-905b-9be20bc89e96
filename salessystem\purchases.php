<?php
// يجب أن تكون هذه السطور الأولى في الملف بالضبط
require_once __DIR__ . '/config/init.php';  // هذا الملف يحتوي على تعريف isLoggedIn()
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

// التحقق من وجود جدول المشتريات
$tables_query = "SHOW TABLES LIKE 'purchases'";
$tables_result = $db->query($tables_query);

if ($tables_result && $tables_result->num_rows == 0) {
    $_SESSION['error'] = "جدول المشتريات غير موجود في قاعدة البيانات";
    error_log("Table 'purchases' does not exist in the database");
}

displayMessages(); // عرض أي رسائل خطأ أو نجاح

// إضافة معلومات تصحيح عن قاعدة البيانات
echo '<!-- Debug: Database info: ' . $db->host_info . ' -->';
echo '<!-- Debug: Database name: ' . $db->query("SELECT DATABASE()")->fetch_row()[0] . ' -->';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><?php echo __('manage_purchases'); ?></h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="add_purchase.php" class="btn btn-success">
            <i class="fas fa-plus"></i> <?php echo __('add_purchase'); ?>
        </a>
        <a href="check_tables.php" class="btn btn-info ms-2">
            <i class="fas fa-database"></i> <?php echo __('check_tables'); ?>
        </a>
    </div>
</div>

<div class="row">
    <!-- القائمة الجانبية -->
    <div class="col-md-3 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i><?php echo __('filter'); ?></h5>
            </div>
            <div class="card-body">
                <form method="GET" action="purchases.php">
                    <div class="mb-3">
                        <label for="search" class="form-label"><?php echo __('search'); ?></label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="<?php echo __('search_placeholder'); ?>" value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                    </div>

                    <div class="mb-3">
                        <label for="customer_id" class="form-label"><?php echo __('customer'); ?></label>
                        <select class="form-select" id="customer_id" name="customer_id">
                            <option value=""><?php echo __('all_customers'); ?></option>
                            <?php
                            $customers = $db->query("SELECT id, name FROM customers ORDER BY name");
                            while ($customer = $customers->fetch_assoc()):
                                $selected = (isset($_GET['customer_id']) && $_GET['customer_id'] == $customer['id']) ? 'selected' : '';
                            ?>
                                <option value="<?php echo $customer['id']; ?>" <?php echo $selected; ?>>
                                    <?php echo htmlspecialchars($customer['name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="date_from" class="form-label"><?php echo __('date_from'); ?></label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo isset($_GET['date_from']) ? htmlspecialchars($_GET['date_from']) : ''; ?>">
                    </div>

                    <div class="mb-3">
                        <label for="date_to" class="form-label"><?php echo __('date_to'); ?></label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo isset($_GET['date_to']) ? htmlspecialchars($_GET['date_to']) : ''; ?>">
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i><?php echo __('search'); ?>
                        </button>
                        <a href="purchases.php" class="btn btn-secondary">
                            <i class="fas fa-redo me-2"></i><?php echo __('reset'); ?>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="card shadow-sm mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i><?php echo __('statistics'); ?></h5>
            </div>
            <div class="card-body">
                <?php
                // التحقق من بنية جدول المشتريات
                $table_structure_query = "SHOW COLUMNS FROM purchases";
                $table_structure_result = $db->query($table_structure_query);

                if ($table_structure_result) {
                    echo '<!-- Debug: Table structure: ';
                    while ($column = $table_structure_result->fetch_assoc()) {
                        echo json_encode($column) . ', ';
                    }
                    echo ' -->';
                }

                // إحصائيات المشتريات
                $stats_query = "SELECT
                                COUNT(id) as total_count,
                                SUM(total_amount) as total_amount,
                                SUM(tax_amount) as total_tax,
                                MIN(date) as first_date,
                                MAX(date) as last_date
                                FROM purchases";

                try {
                    $stats_result = $db->query($stats_query);
                    if ($stats_result) {
                        $stats = $stats_result->fetch_assoc();
                        echo '<!-- Debug: Stats query result: ' . json_encode($stats) . ' -->';
                    } else {
                        echo '<!-- Debug: Stats query error: ' . $db->error . ' -->';
                        $stats = [
                            'total_count' => 0,
                            'total_amount' => 0,
                            'total_tax' => 0
                        ];
                    }
                } catch (Exception $e) {
                    error_log("Error fetching stats: " . $e->getMessage());
                    echo '<!-- Debug: Stats exception: ' . $e->getMessage() . ' -->';
                    $stats = [
                        'total_count' => 0,
                        'total_amount' => 0,
                        'total_tax' => 0
                    ];
                }
                ?>
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <?php echo __('total_purchases'); ?>
                        <span class="badge bg-primary rounded-pill"><?php echo $stats['total_count']; ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <?php echo __('total_amount'); ?>
                        <span class="badge bg-success rounded-pill"><?php echo number_format($stats['total_amount'], 2) . ' ' . __('currency'); ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <?php echo __('total_tax'); ?>
                        <span class="badge bg-info rounded-pill"><?php echo number_format($stats['total_tax'], 2) . ' ' . __('currency'); ?></span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- جدول المشتريات -->
    <div class="col-md-9">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i><?php echo __('purchases_list'); ?></h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th><?php echo __('invoice_number'); ?></th>
                                <th><?php echo __('date'); ?></th>
                                <th><?php echo __('customer'); ?></th>
                                <th><?php echo __('subtotal'); ?></th>
                                <th><?php echo __('tax'); ?></th>
                                <th><?php echo __('total'); ?></th>
                                <th><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                // بناء الاستعلام مع الفلاتر
                                $where_conditions = [];
                                $params = [];
                                $param_types = '';

                                // فلتر البحث
                                if (isset($_GET['search']) && !empty($_GET['search'])) {
                                    $search = '%' . $_GET['search'] . '%';
                                    $where_conditions[] = "(p.invoice_number LIKE ? OR c.name LIKE ?)";
                                    $params[] = $search;
                                    $params[] = $search;
                                    $param_types .= 'ss';
                                }

                                // فلتر العميل - التحقق أولاً من وجود عمود customer_id
                                $check_customer_column = $db->query("SHOW COLUMNS FROM purchases LIKE 'customer_id'");
                                $has_customer_column = ($check_customer_column && $check_customer_column->num_rows > 0);

                                if ($has_customer_column && isset($_GET['customer_id']) && !empty($_GET['customer_id'])) {
                                    $where_conditions[] = "p.customer_id = ?";
                                    $params[] = $_GET['customer_id'];
                                    $param_types .= 'i';
                                }

                                // فلتر التاريخ من
                                if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
                                    $where_conditions[] = "p.date >= ?";
                                    $params[] = $_GET['date_from'];
                                    $param_types .= 's';
                                }

                                // فلتر التاريخ إلى
                                if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
                                    $where_conditions[] = "p.date <= ?";
                                    $params[] = $_GET['date_to'];
                                    $param_types .= 's';
                                }

                                // بناء جملة WHERE
                                $where_clause = '';
                                if (!empty($where_conditions)) {
                                    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
                                }

                                // التحقق من وجود جدول المشتريات مرة أخرى
                                $check_table = $db->query("SHOW TABLES LIKE 'purchases'");
                                if ($check_table && $check_table->num_rows == 0) {
                                    throw new Exception("جدول المشتريات غير موجود في قاعدة البيانات");
                                }

                                // التحقق من وجود الأعمدة المطلوبة في جدول المشتريات
                                $check_customer_id = $db->query("SHOW COLUMNS FROM purchases LIKE 'customer_id'");
                                $has_customer_id = ($check_customer_id && $check_customer_id->num_rows > 0);

                                $check_subtotal = $db->query("SHOW COLUMNS FROM purchases LIKE 'subtotal'");
                                $has_subtotal = ($check_subtotal && $check_subtotal->num_rows > 0);

                                $check_tax_amount = $db->query("SHOW COLUMNS FROM purchases LIKE 'tax_amount'");
                                $has_tax_amount = ($check_tax_amount && $check_tax_amount->num_rows > 0);

                                $check_total_amount = $db->query("SHOW COLUMNS FROM purchases LIKE 'total_amount'");
                                $has_total_amount = ($check_total_amount && $check_total_amount->num_rows > 0);

                                // بناء قائمة الأعمدة بناءً على ما هو متاح
                                $columns = "p.id, p.invoice_number, p.date";

                                if ($has_subtotal) {
                                    $columns .= ", p.subtotal";
                                } else {
                                    $columns .= ", 0 AS subtotal";
                                }

                                if ($has_tax_amount) {
                                    $columns .= ", p.tax_amount";
                                } else {
                                    $columns .= ", 0 AS tax_amount";
                                }

                                if ($has_total_amount) {
                                    $columns .= ", p.total_amount";
                                } else {
                                    $columns .= ", 0 AS total_amount";
                                }

                                // تحديد الأعمدة المطلوبة بشكل صريح بدلاً من استخدام p.*
                                if ($has_customer_id) {
                                    $query = "SELECT $columns, c.name AS customer_name
                                              FROM purchases p
                                              LEFT JOIN customers c ON p.customer_id = c.id
                                              $where_clause
                                              ORDER BY p.id DESC";
                                } else {
                                    // استعلام بديل بدون عمود customer_id
                                    $query = "SELECT $columns, '<?php echo __('not_specified'); ?>' AS customer_name
                                              FROM purchases p
                                              $where_clause
                                              ORDER BY p.id DESC";
                                }

                                echo '<!-- Debug: Final query: ' . $query . ' -->';
                                echo '<!-- Debug: Params: ' . json_encode($params) . ' -->';
                                echo '<!-- Debug: Param types: ' . $param_types . ' -->';

                                if (!empty($params)) {
                                    try {
                                        $stmt = $db->prepare($query);
                                        if (!$stmt) {
                                            throw new Exception("فشل في إعداد الاستعلام: " . $db->error);
                                        }

                                        $stmt->bind_param($param_types, ...$params);
                                        $stmt->execute();
                                        $result = $stmt->get_result();

                                        echo '<!-- Debug: Prepared statement executed successfully -->';
                                    } catch (Exception $stmt_ex) {
                                        echo '<!-- Debug: Prepared statement error: ' . $stmt_ex->getMessage() . ' -->';
                                        throw $stmt_ex;
                                    }
                                } else {
                                    $result = $db->query($query);
                                    echo '<!-- Debug: Direct query executed -->';
                                }

                                if (!$result) {
                                    throw new Exception($db->error);
                                }

                                // إضافة معلومات تصحيح للمطور
                                echo '<!-- Debug: Query executed: ' . $query . ' -->';
                                echo '<!-- Debug: Number of rows: ' . $result->num_rows . ' -->';

                                if ($result->num_rows > 0) {
                                    while ($row = $result->fetch_assoc()):
                                        // إضافة معلومات تصحيح للصف الحالي
                                        echo '<!-- Debug: Row data: ' . json_encode($row) . ' -->';

                                        // التحقق من وجود الحقول المطلوبة
                                        if (isset($row['subtotal']) && $row['subtotal'] > 0) {
                                            // استخدام قيمة subtotal مباشرة إذا كانت موجودة
                                            $subtotal = $row['subtotal'];
                                            echo '<!-- Debug: Using direct subtotal value -->';
                                        } else if (isset($row['total_amount']) && isset($row['tax_amount'])) {
                                            // حساب subtotal من total_amount و tax_amount
                                            $subtotal = $row['total_amount'] - $row['tax_amount'];
                                            echo '<!-- Debug: Calculated subtotal from total and tax -->';
                                        } else {
                                            echo '<!-- Debug: Missing required fields in row -->';
                                            $subtotal = 0; // قيمة افتراضية
                                        }
                                        ?>
                            <tr>
                                <td><?php echo $row['id']; ?></td>
                                <td><?php echo $row['invoice_number']; ?></td>
                                <td><?php echo date('d/m/Y', strtotime($row['date'])); ?></td>
                                <td><?php echo htmlspecialchars($row['customer_name'] ?? __('no_customer')); ?></td>
                                <td><?php echo number_format($subtotal, 2) . ' ' . __('currency'); ?></td>
                                <td><?php echo number_format($row['tax_amount'], 2) . ' ' . __('currency'); ?></td>
                                <td><?php echo number_format($row['total_amount'], 2) . ' ' . __('currency'); ?></td>
                                <td>
                                    <a href="view_purchase.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-info" title="<?php echo __('view'); ?>">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit_purchase.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary" title="<?php echo __('edit'); ?>">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="delete_purchase.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('<?php echo __('confirm_delete_purchase'); ?>');" title="<?php echo __('delete'); ?>">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <a href="print_invoice.php?id=<?php echo $row['id']; ?>&type=purchase" class="btn btn-sm btn-secondary" target="_blank" title="<?php echo __('print'); ?>">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="8" class="text-center">' . __('no_data') . '</td></tr>';
                                }
                            } catch (Exception $e) {
                                $error_message = $e->getMessage();
                                error_log("Error displaying purchases: " . $error_message);

                                // عرض رسالة الخطأ للمطور (سيتم إخفاؤها في الإنتاج)
                                echo '<!-- Debug: Error: ' . htmlspecialchars($error_message) . ' -->';
                                echo '<!-- Debug: Error trace: ' . htmlspecialchars($e->getTraceAsString()) . ' -->';

                                // تعريف وضع التصحيح محليًا (يمكن نقل هذا إلى ملف التكوين لاحقًا)
                                $debug_mode = true; // تغيير إلى false في بيئة الإنتاج

                                // عرض رسالة خطأ أكثر تفصيلاً للمستخدم في بيئة التطوير
                                if ($debug_mode) {
                                    echo '<tr><td colspan="8" class="text-center text-danger">' . __('error') . ': ' . htmlspecialchars($error_message) . '</td></tr>';
                                } else {
                                    echo '<tr><td colspan="8" class="text-center">' . __('error') . '</td></tr>';
                                }
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>