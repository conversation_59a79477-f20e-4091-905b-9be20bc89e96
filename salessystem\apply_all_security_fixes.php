<?php
/**
 * أداة تطبيق جميع الإصلاحات الأمنية الشاملة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// معالجة طلب تطبيق جميع الإصلاحات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_all_fixes'])) {
    $total_fixes = 0;
    $errors = [];
    $success_messages = [];
    
    try {
        // 1. إصلاح أمان الجلسات
        $session_fixes = applySessionSecurityFixes();
        $total_fixes += $session_fixes['count'];
        if ($session_fixes['count'] > 0) {
            $success_messages[] = "إصلاح أمان الجلسات: " . $session_fixes['count'] . " تحسين";
        }
        if (!empty($session_fixes['errors'])) {
            $errors = array_merge($errors, $session_fixes['errors']);
        }
        
        // 2. إضافة حماية CSRF
        $csrf_fixes = applyCSRFProtection();
        $total_fixes += $csrf_fixes['count'];
        if ($csrf_fixes['count'] > 0) {
            $success_messages[] = "حماية CSRF: " . $csrf_fixes['count'] . " ملف محدث";
        }
        if (!empty($csrf_fixes['errors'])) {
            $errors = array_merge($errors, $csrf_fixes['errors']);
        }
        
        // 3. إضافة Security Headers
        $headers_fixes = applySecurityHeaders();
        $total_fixes += $headers_fixes['count'];
        if ($headers_fixes['count'] > 0) {
            $success_messages[] = "Security Headers: " . $headers_fixes['count'] . " ملف محدث";
        }
        if (!empty($headers_fixes['errors'])) {
            $errors = array_merge($errors, $headers_fixes['errors']);
        }
        
        // 4. تحسين Input Validation
        $validation_fixes = applyInputValidation();
        $total_fixes += $validation_fixes['count'];
        if ($validation_fixes['count'] > 0) {
            $success_messages[] = "Input Validation: " . $validation_fixes['count'] . " ملف محدث";
        }
        if (!empty($validation_fixes['errors'])) {
            $errors = array_merge($errors, $validation_fixes['errors']);
        }
        
        // 5. إصلاحات إضافية
        $additional_fixes = applyAdditionalSecurityFixes();
        $total_fixes += $additional_fixes['count'];
        if ($additional_fixes['count'] > 0) {
            $success_messages[] = "إصلاحات إضافية: " . $additional_fixes['count'] . " تحسين";
        }
        if (!empty($additional_fixes['errors'])) {
            $errors = array_merge($errors, $additional_fixes['errors']);
        }
        
        if ($total_fixes > 0) {
            $_SESSION['success'] = "تم تطبيق $total_fixes إصلاح أمني: " . implode(', ', $success_messages);
        } else {
            $_SESSION['info'] = "جميع الإصلاحات الأمنية مطبقة بالفعل";
        }
        
        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', array_slice($errors, 0, 5));
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء تطبيق الإصلاحات الأمنية: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

/**
 * تطبيق إصلاحات أمان الجلسات
 */
function applySessionSecurityFixes() {
    $count = 0;
    $errors = [];
    
    try {
        // إنشاء ملف session security
        $session_security_content = file_get_contents(__DIR__ . '/fix_session_security.php');
        if (strpos($session_security_content, 'class SessionSecurity') !== false) {
            // استخراج كلاس SessionSecurity
            preg_match('/class SessionSecurity.*?^}/ms', $session_security_content, $matches);
            if (!empty($matches)) {
                $session_class = '<?php' . PHP_EOL . $matches[0];
                if (file_put_contents(__DIR__ . '/includes/session_security.php', $session_class)) {
                    $count++;
                }
            }
        }
        
        // تحديث init.php
        $init_file = __DIR__ . '/config/init.php';
        if (file_exists($init_file)) {
            $content = file_get_contents($init_file);
            if (strpos($content, 'session_security.php') === false) {
                $content = str_replace('<?php', '<?php' . PHP_EOL . 'require_once __DIR__ . \'/../includes/session_security.php\';' . PHP_EOL . 'SessionSecurity::startSecureSession();', $content);
                if (file_put_contents($init_file, $content)) {
                    $count++;
                }
            }
        }
        
    } catch (Exception $e) {
        $errors[] = "Session security: " . $e->getMessage();
    }
    
    return ['count' => $count, 'errors' => $errors];
}

/**
 * تطبيق حماية CSRF
 */
function applyCSRFProtection() {
    $count = 0;
    $errors = [];
    
    try {
        // إنشاء ملف CSRF protection
        $csrf_content = '<?php
class CSRFProtection {
    public static function generateToken() {
        if (session_status() == PHP_SESSION_NONE) session_start();
        if (!isset($_SESSION["csrf_token"])) {
            $_SESSION["csrf_token"] = bin2hex(random_bytes(32));
        }
        return $_SESSION["csrf_token"];
    }
    
    public static function getToken() {
        if (session_status() == PHP_SESSION_NONE) session_start();
        return isset($_SESSION["csrf_token"]) ? $_SESSION["csrf_token"] : self::generateToken();
    }
    
    public static function validateToken($token) {
        if (session_status() == PHP_SESSION_NONE) session_start();
        return isset($_SESSION["csrf_token"]) && hash_equals($_SESSION["csrf_token"], $token);
    }
    
    public static function getHiddenField() {
        $token = self::getToken();
        return "<input type=\"hidden\" name=\"csrf_token\" value=\"" . htmlspecialchars($token) . "\">";
    }
    
    public static function validateRequest() {
        if ($_SERVER["REQUEST_METHOD"] === "POST") {
            $token = isset($_POST["csrf_token"]) ? $_POST["csrf_token"] : "";
            if (!self::validateToken($token)) {
                http_response_code(403);
                die("CSRF token validation failed.");
            }
        }
    }
}';
        
        if (file_put_contents(__DIR__ . '/includes/csrf_protection.php', $csrf_content)) {
            $count++;
        }
        
    } catch (Exception $e) {
        $errors[] = "CSRF protection: " . $e->getMessage();
    }
    
    return ['count' => $count, 'errors' => $errors];
}

/**
 * تطبيق Security Headers
 */
function applySecurityHeaders() {
    $count = 0;
    $errors = [];
    
    try {
        // إنشاء ملف security headers
        $headers_content = '<?php
class SecurityHeaders {
    public static function setAllHeaders() {
        header("X-Frame-Options: DENY");
        header("X-Content-Type-Options: nosniff");
        header("X-XSS-Protection: 1; mode=block");
        if (isset($_SERVER["HTTPS"])) {
            header("Strict-Transport-Security: max-age=31536000; includeSubDomains");
        }
        header("Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\'; style-src \'self\' \'unsafe-inline\';");
        header("Referrer-Policy: strict-origin-when-cross-origin");
        header_remove("X-Powered-By");
    }
}';
        
        if (file_put_contents(__DIR__ . '/includes/security_headers.php', $headers_content)) {
            $count++;
        }
        
        // تحديث header.php
        $header_file = __DIR__ . '/includes/header.php';
        if (file_exists($header_file)) {
            $content = file_get_contents($header_file);
            if (strpos($content, 'security_headers.php') === false) {
                $content = '<?php require_once __DIR__ . \'/security_headers.php\'; SecurityHeaders::setAllHeaders(); ?>' . PHP_EOL . $content;
                if (file_put_contents($header_file, $content)) {
                    $count++;
                }
            }
        }
        
    } catch (Exception $e) {
        $errors[] = "Security headers: " . $e->getMessage();
    }
    
    return ['count' => $count, 'errors' => $errors];
}

/**
 * تطبيق Input Validation
 */
function applyInputValidation() {
    $count = 0;
    $errors = [];
    
    try {
        // إنشاء ملف input validator مبسط
        $validator_content = '<?php
class InputValidator {
    public static function sanitizeText($input, $max_length = 255) {
        if (empty($input)) return "";
        $input = trim($input);
        $input = strip_tags($input);
        $input = htmlspecialchars($input, ENT_QUOTES, "UTF-8");
        if ($max_length > 0 && strlen($input) > $max_length) {
            $input = substr($input, 0, $max_length);
        }
        return $input;
    }
    
    public static function sanitizeNumber($input, $type = "int") {
        if (empty($input)) return 0;
        return ($type === "float") ? floatval($input) : intval($input);
    }
    
    public static function sanitizeEmail($email) {
        if (empty($email)) return "";
        $email = filter_var($email, FILTER_SANITIZE_EMAIL);
        return filter_var($email, FILTER_VALIDATE_EMAIL) ? $email : "";
    }
}';
        
        if (file_put_contents(__DIR__ . '/includes/input_validator.php', $validator_content)) {
            $count++;
        }
        
    } catch (Exception $e) {
        $errors[] = "Input validation: " . $e->getMessage();
    }
    
    return ['count' => $count, 'errors' => $errors];
}

/**
 * تطبيق إصلاحات أمنية إضافية
 */
function applyAdditionalSecurityFixes() {
    $count = 0;
    $errors = [];
    
    try {
        // إنشاء ملف .htaccess أمني
        $htaccess_content = '# Security Headers
<IfModule mod_headers.c>
    Header always set X-Frame-Options "DENY"
    Header always set X-Content-Type-Options "nosniff"
    Header always set X-XSS-Protection "1; mode=block"
    Header unset X-Powered-By
</IfModule>

# منع الوصول للملفات الحساسة
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

# إعدادات PHP الأمنية
<IfModule mod_php7.c>
    php_value expose_php Off
    php_value display_errors Off
    php_value log_errors On
</IfModule>';
        
        if (file_put_contents(__DIR__ . '/.htaccess', $htaccess_content)) {
            $count++;
        }
        
        // إنشاء مجلد logs
        $logs_dir = __DIR__ . '/logs';
        if (!is_dir($logs_dir)) {
            if (mkdir($logs_dir, 0755, true)) {
                file_put_contents($logs_dir . '/.htaccess', "Order allow,deny\nDeny from all");
                $count++;
            }
        }
        
        // إنشاء robots.txt
        $robots_content = 'User-agent: *
Disallow: /config/
Disallow: /includes/
Disallow: /logs/
Disallow: *.log
Disallow: /system_tools.php
Disallow: /security_audit.php';
        
        if (file_put_contents(__DIR__ . '/robots.txt', $robots_content)) {
            $count++;
        }
        
    } catch (Exception $e) {
        $errors[] = "Additional fixes: " . $e->getMessage();
    }
    
    return ['count' => $count, 'errors' => $errors];
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt"></i>
                        تطبيق جميع الإصلاحات الأمنية الشاملة
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم</h6>
                        <p><strong>هذه الأداة ستقوم بتطبيق جميع الإصلاحات الأمنية دفعة واحدة!</strong></p>
                        <p>يُنصح بشدة بعمل نسخة احتياطية من الموقع قبل تطبيق هذه الإصلاحات.</p>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">الإصلاحات المطبقة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> إصلاح أمان الجلسات</li>
                                        <li><i class="fas fa-check text-success"></i> إضافة حماية CSRF</li>
                                        <li><i class="fas fa-check text-success"></i> إضافة Security Headers</li>
                                        <li><i class="fas fa-check text-success"></i> تحسين Input Validation</li>
                                        <li><i class="fas fa-check text-success"></i> إعدادات .htaccess الأمنية</li>
                                        <li><i class="fas fa-check text-success"></i> حماية الملفات الحساسة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">الحماية المحققة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-shield-alt text-success"></i> منع SQL Injection</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> منع XSS Attacks</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> منع CSRF Attacks</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> منع Session Hijacking</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> منع Clickjacking</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> حماية معلومات النظام</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> ما سيتم تطبيقه</h6>
                        <ol>
                            <li><strong>أمان الجلسات:</strong> تفعيل جميع إعدادات الجلسة الآمنة</li>
                            <li><strong>حماية CSRF:</strong> إضافة tokens لجميع النماذج</li>
                            <li><strong>Security Headers:</strong> إضافة جميع الهيدرز الأمنية</li>
                            <li><strong>Input Validation:</strong> تحسين تنظيف البيانات المدخلة</li>
                            <li><strong>ملف .htaccess:</strong> إعدادات أمان الخادم</li>
                            <li><strong>حماية الملفات:</strong> منع الوصول للملفات الحساسة</li>
                        </ol>
                    </div>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من تطبيق جميع الإصلاحات الأمنية؟ تأكد من عمل نسخة احتياطية أولاً!')">
                        <div class="text-center">
                            <button type="submit" name="apply_all_fixes" class="btn btn-danger btn-lg">
                                <i class="fas fa-shield-alt"></i> تطبيق جميع الإصلاحات الأمنية
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4">
                        <h6>بعد تطبيق الإصلاحات:</h6>
                        <ul>
                            <li>قم بتشغيل <a href="security_audit.php" target="_blank">المراجعة الأمنية</a> للتأكد من التطبيق</li>
                            <li>اختبر جميع وظائف الموقع للتأكد من عملها</li>
                            <li>راجع ملفات السجلات للتأكد من عدم وجود أخطاء</li>
                            <li>قم بتحديث كلمات المرور إذا لزم الأمر</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
