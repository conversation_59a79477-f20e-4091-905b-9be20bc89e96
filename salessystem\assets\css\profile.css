/**
 * تنسيقات خاصة بصفحة الملف الشخصي
 */

/* الصورة الرمزية */
.avatar-placeholder {
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.avatar-placeholder:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

/* بطاقة الملف الشخصي */
.profile-card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    overflow: hidden;
}

.profile-card .card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-bottom: none;
    padding: 1.5rem;
}

.profile-card .card-body {
    padding: 2rem;
}

/* جدول المعلومات */
.info-table {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
}

.info-table td {
    padding: 0.75rem 0;
    border: none;
}

.info-table td:first-child {
    color: #6c757d;
    font-weight: 500;
}

/* نموذج التحديث */
.update-form {
    background: #ffffff;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* قسم كلمة المرور */
.password-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
    border-left: 4px solid #ffc107;
}

.password-section h6 {
    color: #495057;
    margin-bottom: 1rem;
}

/* الأزرار */
.btn-profile {
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-profile:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* الإحصائيات */
.stats-list {
    background: #e9ecef;
    border-radius: 10px;
    padding: 1rem;
}

.stats-list li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.stats-list li:last-child {
    border-bottom: none;
}

.stats-list i {
    width: 20px;
    text-align: center;
    margin-right: 0.5rem;
}

/* الرسائل */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* التحقق من كلمة المرور */
.password-strength {
    margin-top: 0.5rem;
}

.password-strength-bar {
    height: 4px;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.password-strength-weak {
    background-color: #dc3545;
    width: 33%;
}

.password-strength-medium {
    background-color: #ffc107;
    width: 66%;
}

.password-strength-strong {
    background-color: #28a745;
    width: 100%;
}

/* الرسوم المتحركة */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* التصميم المتجاوب */
@media (max-width: 768px) {
    .profile-card .card-body {
        padding: 1rem;
    }
    
    .avatar-placeholder {
        width: 80px !important;
        height: 80px !important;
        font-size: 32px !important;
    }
    
    .update-form {
        padding: 1rem;
    }
    
    .password-section {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .btn-profile {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .info-table {
        padding: 0.5rem;
    }
    
    .stats-list {
        padding: 0.5rem;
    }
}

/* تحسينات إضافية */
.card-header h4 {
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.text-muted {
    font-size: 0.9rem;
}

/* تأثيرات التركيز */
.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسين الجداول */
.table td {
    vertical-align: middle;
}

/* تحسين الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 15px;
}

/* تحسين الأيقونات */
.fas, .far {
    margin-right: 0.5rem;
}

/* تحسين المسافات */
.mb-custom {
    margin-bottom: 2rem;
}

.mt-custom {
    margin-top: 2rem;
}
