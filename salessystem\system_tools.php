<?php
/**
 * صفحة أدوات النظام والفحص والاختبار
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// فحص حالة النظام
$system_status = [];

// فحص الجداول الأساسية
$required_tables = ['customers', 'sales', 'purchases', 'products', 'settings'];
$system_status['tables'] = [];
foreach ($required_tables as $table) {
    try {
        $check = $db->query("SHOW TABLES LIKE '$table'");
        $system_status['tables'][$table] = ($check && $check->num_rows > 0);
    } catch (Exception $e) {
        $system_status['tables'][$table] = false;
    }
}

// فحص الملفات المهمة
$important_files = [
    'config/init.php' => 'ملف الإعدادات الأساسي',
    'includes/functions.php' => 'ملف الدوال المساعدة',
    'includes/auth.php' => 'ملف المصادقة',
    'languages/ar/lang.php' => 'ملف اللغة العربية',
    'languages/en/lang.php' => 'ملف اللغة الإنجليزية'
];

$system_status['files'] = [];
foreach ($important_files as $file => $description) {
    $system_status['files'][$file] = [
        'exists' => file_exists(__DIR__ . '/' . $file),
        'description' => $description
    ];
}

// فحص إعدادات PHP
$system_status['php'] = [
    'version' => phpversion(),
    'mysqli_enabled' => extension_loaded('mysqli'),
    'session_enabled' => extension_loaded('session'),
    'json_enabled' => extension_loaded('json'),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time')
];

// فحص قاعدة البيانات
try {
    $system_status['database'] = [
        'connected' => true,
        'version' => $db->server_info,
        'charset' => $db->character_set_name()
    ];
    
    // فحص عدد السجلات
    foreach ($required_tables as $table) {
        if ($system_status['tables'][$table]) {
            $count_result = $db->query("SELECT COUNT(*) as count FROM $table");
            $system_status['database'][$table . '_count'] = $count_result ? $count_result->fetch_assoc()['count'] : 0;
        }
    }
} catch (Exception $e) {
    $system_status['database'] = [
        'connected' => false,
        'error' => $e->getMessage()
    ];
}

displayMessages();
?>

<!-- تضمين ملف CSS الخاص بأدوات النظام -->
<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-tools"></i>
                        أدوات النظام والفحص والاختبار
                    </h4>
                </div>
                <div class="card-body">
                    <!-- التبويبات -->
                    <ul class="nav nav-tabs" id="systemToolsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="status-tab" data-bs-toggle="tab" data-bs-target="#status" type="button" role="tab">
                                <i class="fas fa-heartbeat"></i> حالة النظام
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                                <i class="fas fa-shield-alt"></i> أدوات الأمان
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="database-tab" data-bs-toggle="tab" data-bs-target="#database" type="button" role="tab">
                                <i class="fas fa-database"></i> أدوات قاعدة البيانات
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="testing-tab" data-bs-toggle="tab" data-bs-target="#testing" type="button" role="tab">
                                <i class="fas fa-vial"></i> أدوات الاختبار
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="maintenance-tab" data-bs-toggle="tab" data-bs-target="#maintenance" type="button" role="tab">
                                <i class="fas fa-wrench"></i> أدوات الصيانة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="development-tab" data-bs-toggle="tab" data-bs-target="#development" type="button" role="tab">
                                <i class="fas fa-code"></i> أدوات التطوير
                            </button>
                        </li>
                    </ul>
                    
                    <div class="tab-content mt-4" id="systemToolsTabContent">
                        <!-- تبويب حالة النظام -->
                        <div class="tab-pane fade show active" id="status" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-heartbeat text-success"></i>
                                حالة النظام العامة
                            </h5>
                            
                            <div class="row">
                                <!-- حالة الجداول -->
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-table"></i>
                                                الجداول الأساسية
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <?php foreach ($system_status['tables'] as $table => $exists): ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span><?php echo $table; ?>:</span>
                                                <span class="badge <?php echo $exists ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $exists ? '✓ موجود' : '✗ مفقود'; ?>
                                                </span>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- حالة الملفات -->
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-file-code"></i>
                                                الملفات المهمة
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <?php foreach ($system_status['files'] as $file => $data): ?>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span title="<?php echo $data['description']; ?>"><?php echo basename($file); ?>:</span>
                                                <span class="badge <?php echo $data['exists'] ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $data['exists'] ? '✓' : '✗'; ?>
                                                </span>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- معلومات PHP -->
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">
                                                <i class="fab fa-php"></i>
                                                معلومات PHP
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>إصدار PHP:</span>
                                                <span class="badge bg-info"><?php echo $system_status['php']['version']; ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>MySQLi:</span>
                                                <span class="badge <?php echo $system_status['php']['mysqli_enabled'] ? 'bg-success' : 'bg-danger'; ?>">
                                                    <?php echo $system_status['php']['mysqli_enabled'] ? '✓' : '✗'; ?>
                                                </span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>الذاكرة المحددة:</span>
                                                <span class="badge bg-secondary"><?php echo $system_status['php']['memory_limit']; ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>وقت التنفيذ الأقصى:</span>
                                                <span class="badge bg-secondary"><?php echo $system_status['php']['max_execution_time']; ?>s</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- معلومات قاعدة البيانات -->
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-database"></i>
                                                قاعدة البيانات
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <?php if ($system_status['database']['connected']): ?>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span>الاتصال:</span>
                                                    <span class="badge bg-success">✓ متصل</span>
                                                </div>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span>إصدار MySQL:</span>
                                                    <span class="badge bg-info"><?php echo $system_status['database']['version']; ?></span>
                                                </div>
                                                <div class="d-flex justify-content-between mb-2">
                                                    <span>ترميز الأحرف:</span>
                                                    <span class="badge bg-secondary"><?php echo $system_status['database']['charset']; ?></span>
                                                </div>
                                                
                                                <hr>
                                                <h6>عدد السجلات:</h6>
                                                <?php foreach ($required_tables as $table): ?>
                                                    <?php if (isset($system_status['database'][$table . '_count'])): ?>
                                                    <div class="d-flex justify-content-between">
                                                        <small><?php echo $table; ?>:</small>
                                                        <span class="badge bg-primary"><?php echo $system_status['database'][$table . '_count']; ?></span>
                                                    </div>
                                                    <?php endif; ?>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <div class="alert alert-danger">
                                                    <i class="fas fa-times-circle"></i>
                                                    خطأ في الاتصال: <?php echo $system_status['database']['error']; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب أدوات الأمان -->
                        <div class="tab-pane fade" id="security" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-shield-alt text-danger"></i>
                                أدوات الأمان والحماية
                            </h5>

                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم</h6>
                                <p>أدوات الأمان تقوم بتعديلات مهمة على النظام. يُنصح بعمل نسخة احتياطية قبل استخدامها.</p>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-search"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>المراجعة الأمنية الشاملة</h6>
                                            <p>فحص شامل للمشاكل الأمنية في النظام</p>
                                            <a href="security_audit.php" class="btn btn-danger btn-sm" target="_blank">
                                                <i class="fas fa-shield-alt"></i> فحص الأمان
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-user-lock"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>إصلاح أمان الجلسات</h6>
                                            <p>تطبيق إعدادات الجلسة الآمنة</p>
                                            <a href="fix_session_security.php" class="btn btn-primary btn-sm" target="_blank">
                                                <i class="fas fa-shield-alt"></i> إصلاح الجلسات
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-shield-alt"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>إضافة حماية CSRF</h6>
                                            <p>حماية النماذج من هجمات CSRF</p>
                                            <a href="add_csrf_protection.php" class="btn btn-success btn-sm" target="_blank">
                                                <i class="fas fa-shield-alt"></i> إضافة CSRF
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-code"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>إضافة Security Headers</h6>
                                            <p>إضافة هيدرز الأمان للحماية</p>
                                            <a href="add_security_headers.php" class="btn btn-warning btn-sm" target="_blank">
                                                <i class="fas fa-code"></i> إضافة Headers
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-filter"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>تحسين Input Validation</h6>
                                            <p>تحسين تنظيف البيانات المدخلة</p>
                                            <a href="improve_input_validation.php" class="btn btn-info btn-sm" target="_blank">
                                                <i class="fas fa-filter"></i> تحسين التحقق
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-shield-alt"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>تطبيق جميع الإصلاحات</h6>
                                            <p>تطبيق جميع الإصلاحات الأمنية دفعة واحدة</p>
                                            <a href="apply_all_security_fixes.php" class="btn btn-danger btn-sm" target="_blank">
                                                <i class="fas fa-shield-alt"></i> تطبيق الكل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h6>ترتيب الاستخدام الموصى به:</h6>
                                <ol>
                                    <li><strong>المراجعة الأمنية:</strong> ابدأ بفحص الوضع الأمني الحالي</li>
                                    <li><strong>الإصلاحات الفردية:</strong> طبق الإصلاحات واحداً تلو الآخر</li>
                                    <li><strong>أو الإصلاح الشامل:</strong> طبق جميع الإصلاحات دفعة واحدة</li>
                                    <li><strong>إعادة الفحص:</strong> تأكد من تطبيق الإصلاحات بنجاح</li>
                                </ol>
                            </div>
                        </div>

                        <!-- تبويب أدوات قاعدة البيانات -->
                        <div class="tab-pane fade" id="database" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-database text-primary"></i>
                                أدوات إدارة قاعدة البيانات
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-heartbeat"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>فحص صحة قاعدة البيانات</h6>
                                            <p>فحص شامل لصحة وسلامة قاعدة البيانات</p>
                                            <a href="database_health_check.php" class="btn btn-success btn-sm" target="_blank">
                                                <i class="fas fa-heartbeat"></i> فحص الصحة
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-table"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>فحص الجداول</h6>
                                            <p>فحص وإنشاء الجداول المفقودة</p>
                                            <a href="check_tables.php" class="btn btn-primary btn-sm" target="_blank">
                                                <i class="fas fa-search"></i> فحص الجداول
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-cogs"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>إنشاء جدول الإعدادات</h6>
                                            <p>إنشاء وتهيئة جدول الإعدادات</p>
                                            <a href="create_settings_table.php" class="btn btn-warning btn-sm" target="_blank">
                                                <i class="fas fa-plus"></i> إنشاء الجدول
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-seedling"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>إضافة البيانات الافتراضية</h6>
                                            <p>إضافة الإعدادات الافتراضية لقاعدة البيانات</p>
                                            <a href="initialize_default_data.php" class="btn btn-success btn-sm" target="_blank">
                                                <i class="fas fa-seedling"></i> إضافة البيانات
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-envelope-plus"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>إضافة عمود البريد الإلكتروني</h6>
                                            <p>إضافة عمود البريد للجداول الموجودة</p>
                                            <a href="add_email_column.php" class="btn btn-success btn-sm" target="_blank">
                                                <i class="fas fa-plus"></i> إضافة العمود
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-sync"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>تحديث بنية قاعدة البيانات</h6>
                                            <p>مراجعة وتحديث بنية جميع الجداول والأعمدة</p>
                                            <a href="update_database_schema.php" class="btn btn-primary btn-sm" target="_blank">
                                                <i class="fas fa-database"></i> تحديث البنية
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-sync-alt"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>إصلاح مشاكل MySQLi Sync</h6>
                                            <p>إصلاح أخطاء "Commands out of sync"</p>
                                            <a href="fix_mysqli_sync_errors.php" class="btn btn-warning btn-sm" target="_blank">
                                                <i class="fas fa-sync-alt"></i> إصلاح المشاكل
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-wrench"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>إصلاح جدول العملاء</h6>
                                            <p>إصلاح وتحديث بنية جدول العملاء</p>
                                            <a href="fix_customer_table.php" class="btn btn-info btn-sm" target="_blank">
                                                <i class="fas fa-tools"></i> إصلاح الجدول
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- تبويب أدوات الاختبار -->
                        <div class="tab-pane fade" id="testing" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-vial text-info"></i>
                                أدوات الاختبار والتحقق
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-clipboard-check"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>اختبار النظام الشامل</h6>
                                            <p>فحص شامل لجميع مكونات النظام</p>
                                            <a href="test_system.php" class="btn btn-primary btn-sm" target="_blank">
                                                <i class="fas fa-play"></i> تشغيل الاختبار
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-globe-americas"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>ترجمة شاملة لجميع النصوص</h6>
                                            <p>ترجمة شاملة لجميع النصوص العربية المكتشفة</p>
                                            <a href="comprehensive_arabic_translation.php" class="btn btn-gradient-primary btn-sm" target="_blank">
                                                <i class="fas fa-globe-americas"></i> ترجمة شاملة
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-language"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>ترجمة المصطلحات العربية</h6>
                                            <p>ترجمة جميع المصطلحات العربية في الكود</p>
                                            <a href="translate_arabic_terms.php" class="btn btn-success btn-sm" target="_blank">
                                                <i class="fas fa-language"></i> ترجمة المصطلحات
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-globe"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>ترجمة شاملة للنصوص</h6>
                                            <p>ترجمة جميع النصوص غير المترجمة تلقائياً</p>
                                            <a href="translate_all_texts.php" class="btn btn-primary btn-sm" target="_blank">
                                                <i class="fas fa-globe"></i> ترجمة شاملة
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-magic"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>تطبيق شامل لجميع الترجمات</h6>
                                            <p>تطبيق جميع الترجمات على الملفات تلقائياً</p>
                                            <a href="apply_comprehensive_translations.php" class="btn btn-gradient-success btn-sm" target="_blank">
                                                <i class="fas fa-magic"></i> تطبيق شامل
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-exchange-alt"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>استبدال النصوص المكتوبة مباشرة</h6>
                                            <p>استبدال النصوص بدالة الترجمة تلقائياً</p>
                                            <a href="replace_hardcoded_texts.php" class="btn btn-warning btn-sm" target="_blank">
                                                <i class="fas fa-exchange-alt"></i> استبدال النصوص
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-chart-pie"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>تقرير شامل للنصوص العربية</h6>
                                            <p>تقرير نهائي لجميع النصوص المكتشفة</p>
                                            <a href="final_arabic_texts_report.php" class="btn btn-gradient-dark btn-sm" target="_blank">
                                                <i class="fas fa-chart-pie"></i> التقرير النهائي
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-search-plus"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>فحص المصطلحات العربية</h6>
                                            <p>فحص شامل للمصطلحات العربية في الكود</p>
                                            <a href="scan_arabic_terms.php" class="btn btn-info btn-sm" target="_blank">
                                                <i class="fas fa-search-plus"></i> فحص المصطلحات
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-scan"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>فحص النصوص غير المترجمة</h6>
                                            <p>فحص شامل للنصوص التي تحتاج ترجمة</p>
                                            <a href="scan_untranslated_texts.php" class="btn btn-secondary btn-sm" target="_blank">
                                                <i class="fas fa-scan"></i> فحص النصوص
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-search"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>مراجعة الترجمات الشاملة</h6>
                                            <p>مراجعة وإصلاح جميع الترجمات والنصوص</p>
                                            <a href="review_translations.php" class="btn btn-info btn-sm" target="_blank">
                                                <i class="fas fa-search"></i> مراجعة شاملة
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-globe"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>اختبار الترجمات</h6>
                                            <p>فحص اكتمال ملفات الترجمة</p>
                                            <a href="test_translations.php" class="btn btn-info btn-sm" target="_blank">
                                                <i class="fas fa-vial"></i> اختبار الترجمات
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>اختبار الترجمات المحدثة</h6>
                                            <p>فحص الترجمات الجديدة والمحدثة</p>
                                            <a href="test_updated_translations.php" class="btn btn-success btn-sm" target="_blank">
                                                <i class="fas fa-check"></i> اختبار المحدثة
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-user-check"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>اختبار الملف الشخصي</h6>
                                            <p>فحص صفحة الملف الشخصي والنوافذ المنبثقة</p>
                                            <a href="test_profile_page.php" class="btn btn-secondary btn-sm" target="_blank">
                                                <i class="fas fa-user"></i> اختبار الملف الشخصي
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-window-restore"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>اختبار النوافذ المنبثقة</h6>
                                            <p>فحص النوافذ المنبثقة والتفاعلات</p>
                                            <a href="test_profile_modals.php" class="btn btn-info btn-sm" target="_blank">
                                                <i class="fas fa-window-maximize"></i> اختبار النوافذ
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-cogs"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>اختبار صفحة الإعدادات</h6>
                                            <p>فحص صفحة الإعدادات والتبويبات</p>
                                            <a href="test_settings_page.php" class="btn btn-warning btn-sm" target="_blank">
                                                <i class="fas fa-sliders-h"></i> اختبار الإعدادات
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-envelope-check"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>اختبار تكامل البريد الإلكتروني</h6>
                                            <p>فحص تكامل حقل البريد الإلكتروني</p>
                                            <a href="test_email_field_integration.php" class="btn btn-success btn-sm" target="_blank">
                                                <i class="fas fa-envelope"></i> اختبار البريد
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-code"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>إصلاح النصوص المكتوبة مباشرة</h6>
                                            <p>إصلاح النصوص المكتوبة مباشرة في الكود</p>
                                            <a href="fix_hardcoded_texts.php" class="btn btn-danger btn-sm" target="_blank">
                                                <i class="fas fa-magic"></i> إصلاح النصوص
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب أدوات الصيانة -->
                        <div class="tab-pane fade" id="maintenance" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-wrench text-warning"></i>
                                أدوات الصيانة والإصلاح
                            </h5>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-database"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>إضافة بيانات تجريبية</h6>
                                            <p>إضافة بيانات تجريبية للاختبار</p>
                                            <a href="add_sample_data.php" class="btn btn-success btn-sm" target="_blank">
                                                <i class="fas fa-plus"></i> إضافة البيانات
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-language"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>إصلاح الترجمات</h6>
                                            <p>إصلاح وإضافة الترجمات المفقودة</p>
                                            <a href="fix_translations.php" class="btn btn-warning btn-sm" target="_blank">
                                                <i class="fas fa-tools"></i> إصلاح الترجمات
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-shopping-cart"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>اختبار إصلاح المشتريات</h6>
                                            <p>فحص وإصلاح مشاكل المشتريات</p>
                                            <a href="test_purchase_fix.php" class="btn btn-info btn-sm" target="_blank">
                                                <i class="fas fa-cart-plus"></i> اختبار المشتريات
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-user-plus"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>اختبار ميزة إضافة العميل</h6>
                                            <p>فحص ميزة إضافة العميل الجديد</p>
                                            <a href="test_add_customer_feature.php" class="btn btn-primary btn-sm" target="_blank">
                                                <i class="fas fa-user-check"></i> اختبار الميزة
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-check-double"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>اختبار تطابق الحقول</h6>
                                            <p>فحص تطابق حقول العملاء</p>
                                            <a href="test_customer_fields_match.php" class="btn btn-secondary btn-sm" target="_blank">
                                                <i class="fas fa-equals"></i> اختبار التطابق
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-code"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>اختبار AJAX للعملاء</h6>
                                            <p>فحص وظائف AJAX لإضافة العملاء</p>
                                            <a href="test_ajax_customer.php" class="btn btn-info btn-sm" target="_blank">
                                                <i class="fas fa-sync"></i> اختبار AJAX
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب أدوات التطوير -->
                        <div class="tab-pane fade" id="development" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-code text-secondary"></i>
                                أدوات التطوير والتحليل
                            </h5>

                            <div class="row">
                                <div class="col-md-12 mb-4">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-info-circle"></i> معلومات مهمة</h6>
                                        <p>هذه الأدوات مخصصة للمطورين والمسؤولين فقط. استخدمها بحذر.</p>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-bug"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>معلومات PHP</h6>
                                            <p>عرض معلومات PHP التفصيلية</p>
                                            <button class="btn btn-danger btn-sm" onclick="showPHPInfo()">
                                                <i class="fas fa-info"></i> عرض معلومات PHP
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>سجلات الأخطاء</h6>
                                            <p>عرض سجلات أخطاء النظام</p>
                                            <button class="btn btn-warning btn-sm" onclick="showErrorLogs()">
                                                <i class="fas fa-list"></i> عرض السجلات
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-download"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>تصدير قاعدة البيانات</h6>
                                            <p>إنشاء نسخة احتياطية من قاعدة البيانات</p>
                                            <button class="btn btn-success btn-sm" onclick="exportDatabase()">
                                                <i class="fas fa-database"></i> تصدير قاعدة البيانات
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="tool-card">
                                        <div class="tool-icon">
                                            <i class="fas fa-broom"></i>
                                        </div>
                                        <div class="tool-content">
                                            <h6>تنظيف الجلسات</h6>
                                            <p>حذف الجلسات المنتهية الصلاحية</p>
                                            <button class="btn btn-secondary btn-sm" onclick="cleanSessions()">
                                                <i class="fas fa-trash"></i> تنظيف الجلسات
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-12 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-dark text-white">
                                            <h6 class="mb-0">
                                                <i class="fas fa-terminal"></i>
                                                وحدة التحكم
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="console-output" class="console-output mb-3" style="height: 200px; overflow-y: auto; background: #000; color: #0f0; padding: 10px; font-family: monospace;">
                                                <div>نظام أدوات التطوير جاهز...</div>
                                                <div>اكتب الأوامر أدناه:</div>
                                            </div>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="text" class="form-control" id="console-input" placeholder="اكتب أمر...">
                                                <button class="btn btn-primary" onclick="executeCommand()">
                                                    <i class="fas fa-play"></i> تنفيذ
                                                </button>
                                            </div>
                                            <small class="text-muted">
                                                الأوامر المتاحة: status, tables, files, clear, help
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// دوال أدوات التطوير
function showPHPInfo() {
    if (confirm('هل أنت متأكد من عرض معلومات PHP؟ قد تحتوي على معلومات حساسة.')) {
        window.open('data:text/html,<?php echo urlencode(ob_get_clean()); ob_start(); phpinfo(); $phpinfo = ob_get_clean(); echo htmlspecialchars($phpinfo); ?>', '_blank');
    }
}

function showErrorLogs() {
    addToConsole('جاري البحث عن سجلات الأخطاء...');
    // يمكن إضافة كود لقراءة ملفات السجلات هنا
    addToConsole('لا توجد أخطاء حديثة.');
}

function exportDatabase() {
    if (confirm('هل تريد تصدير قاعدة البيانات؟')) {
        addToConsole('جاري تصدير قاعدة البيانات...');
        // يمكن إضافة كود التصدير هنا
        addToConsole('تم تصدير قاعدة البيانات بنجاح.');
    }
}

function cleanSessions() {
    if (confirm('هل تريد تنظيف الجلسات المنتهية الصلاحية؟')) {
        addToConsole('جاري تنظيف الجلسات...');
        // يمكن إضافة كود تنظيف الجلسات هنا
        addToConsole('تم تنظيف الجلسات بنجاح.');
    }
}

function executeCommand() {
    const input = document.getElementById('console-input');
    const command = input.value.trim();

    if (!command) return;

    addToConsole('$ ' + command);

    switch(command.toLowerCase()) {
        case 'status':
            addToConsole('حالة النظام: <?php echo $system_status['database']['connected'] ? 'متصل' : 'غير متصل'; ?>');
            addToConsole('إصدار PHP: <?php echo $system_status['php']['version']; ?>');
            break;
        case 'tables':
            addToConsole('الجداول الموجودة:');
            <?php foreach ($system_status['tables'] as $table => $exists): ?>
            addToConsole('  <?php echo $table; ?>: <?php echo $exists ? 'موجود' : 'مفقود'; ?>');
            <?php endforeach; ?>
            break;
        case 'files':
            addToConsole('الملفات المهمة:');
            <?php foreach ($system_status['files'] as $file => $data): ?>
            addToConsole('  <?php echo basename($file); ?>: <?php echo $data['exists'] ? 'موجود' : 'مفقود'; ?>');
            <?php endforeach; ?>
            break;
        case 'clear':
            document.getElementById('console-output').innerHTML = '';
            break;
        case 'help':
            addToConsole('الأوامر المتاحة:');
            addToConsole('  status - عرض حالة النظام');
            addToConsole('  tables - عرض حالة الجداول');
            addToConsole('  files - عرض حالة الملفات');
            addToConsole('  clear - مسح الشاشة');
            addToConsole('  help - عرض هذه المساعدة');
            break;
        default:
            addToConsole('أمر غير معروف: ' + command);
            addToConsole('اكتب "help" لعرض الأوامر المتاحة');
    }

    input.value = '';
}

function addToConsole(message) {
    const output = document.getElementById('console-output');
    const div = document.createElement('div');
    div.textContent = message;
    output.appendChild(div);
    output.scrollTop = output.scrollHeight;
}

// تفعيل Enter في وحدة التحكم
document.getElementById('console-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        executeCommand();
    }
});
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
