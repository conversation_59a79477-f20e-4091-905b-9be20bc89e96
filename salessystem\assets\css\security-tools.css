/**
 * أنماط أدوات الأمان
 */

/* بطاقات أدوات الأمان */
.security-tool-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.security-tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.security-tool-card .card-header {
    border: none;
    padding: 1.5rem;
    position: relative;
}

.security-tool-card .card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #dc3545, #fd7e14, #ffc107);
}

/* أيقونات الأمان */
.security-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, #dc3545, #c82333);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.security-icon.warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.security-icon.success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.security-icon.info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

/* مؤشر النتيجة الأمنية */
.security-score {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
}

.security-score-circle {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(
        from 0deg,
        #dc3545 0deg,
        #ffc107 90deg,
        #28a745 180deg
    );
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.security-score-inner {
    width: 80%;
    height: 80%;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
}

.security-score-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

.security-score-label {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

/* شارات الحالة */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.critical {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    box-shadow: 0 2px 10px rgba(220, 53, 69, 0.3);
}

.status-badge.warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    box-shadow: 0 2px 10px rgba(255, 193, 7, 0.3);
}

.status-badge.good {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
    box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
}

.status-badge.excellent {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
}

/* قائمة المشاكل الأمنية */
.security-issues-list {
    list-style: none;
    padding: 0;
}

.security-issues-list li {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
    border-left: 4px solid #dc3545;
    background: rgba(220, 53, 69, 0.05);
    transition: all 0.3s ease;
}

.security-issues-list li:hover {
    background: rgba(220, 53, 69, 0.1);
    transform: translateX(5px);
}

.security-issues-list li.warning {
    border-left-color: #ffc107;
    background: rgba(255, 193, 7, 0.05);
}

.security-issues-list li.warning:hover {
    background: rgba(255, 193, 7, 0.1);
}

.security-issues-list li.info {
    border-left-color: #17a2b8;
    background: rgba(23, 162, 184, 0.05);
}

.security-issues-list li.info:hover {
    background: rgba(23, 162, 184, 0.1);
}

/* شريط التقدم الأمني */
.security-progress {
    height: 25px;
    border-radius: 15px;
    background: #e9ecef;
    overflow: hidden;
    position: relative;
}

.security-progress-bar {
    height: 100%;
    border-radius: 15px;
    transition: width 1s ease-in-out;
    position: relative;
    overflow: hidden;
}

.security-progress-bar.critical {
    background: linear-gradient(90deg, #dc3545, #c82333);
}

.security-progress-bar.warning {
    background: linear-gradient(90deg, #ffc107, #e0a800);
}

.security-progress-bar.good {
    background: linear-gradient(90deg, #28a745, #1e7e34);
}

.security-progress-bar.excellent {
    background: linear-gradient(90deg, #007bff, #0056b3);
}

.security-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* أزرار الإصلاح */
.fix-button {
    border: none;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.fix-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    transition: left 0.5s ease;
}

.fix-button:hover::before {
    left: 100%;
}

.fix-button.critical {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.fix-button.critical:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
}

/* تحذيرات الأمان */
.security-warning {
    border: none;
    border-radius: 15px;
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-left: 5px solid #ffc107;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.security-warning.critical {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border-left-color: #dc3545;
}

.security-warning h6 {
    color: #856404;
    font-weight: 700;
    margin-bottom: 0.75rem;
}

.security-warning.critical h6 {
    color: #721c24;
}

/* إحصائيات الأمان */
.security-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.security-stat-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.security-stat-card:hover {
    transform: translateY(-3px);
}

.security-stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.security-stat-label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* تبويبات الأمان */
.security-tabs .nav-link {
    border: none;
    border-radius: 25px;
    padding: 0.75rem 1.5rem;
    margin: 0 0.25rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.security-tabs .nav-link.active {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.security-tabs .nav-link:hover:not(.active) {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

/* رسائل النجاح */
.security-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: none;
    border-radius: 15px;
    border-left: 5px solid #28a745;
    padding: 1.5rem;
    color: #155724;
}

.security-success h6 {
    color: #155724;
    font-weight: 700;
}

/* تأثيرات الحركة */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .security-stats {
        grid-template-columns: 1fr;
    }
    
    .security-score {
        width: 100px;
        height: 100px;
    }
    
    .security-score-value {
        font-size: 1.25rem;
    }
    
    .fix-button {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
