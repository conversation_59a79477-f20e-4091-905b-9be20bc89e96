<?php
require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';
redirectIfNotLoggedIn();

// 1. الحصول على اتصال قاعدة البيانات مع معالجة الأخطاء
$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    die("فشل الاتصال بقاعدة البيانات: " . ($db ? $db->connect_error : "اتصال غير موجود"));
}

// تأكد من تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

// 2. استعلام المبيعات اليومية
$today = date('Y-m-d');
$today_sales = 0;

try {
    $sales_stmt = $db->prepare("SELECT SUM(total_amount) FROM sales WHERE date = ?");
    if ($sales_stmt) {
        $sales_stmt->bind_param("s", $today);
        $sales_stmt->execute();
        $sales_stmt->bind_result($today_sales);
        $sales_stmt->fetch();
        $sales_stmt->close();
    } else {
        error_log("خطأ في إعداد استعلام المبيعات: " . $db->error);
    }
} catch (Exception $e) {
    error_log("استثناء في استعلام المبيعات: " . $e->getMessage());
}

// تنظيف النتائج مرة أخرى
$db = resetDBConnection($db);

// 3. استعلام المشتريات اليومية
$today_purchases = 0;

try {
    // التحقق من وجود جدول المشتريات أولاً
    $check_purchases_table = $db->query("SHOW TABLES LIKE 'purchases'");
    if ($check_purchases_table && $check_purchases_table->num_rows > 0) {
        $purchases_stmt = $db->prepare("SELECT SUM(total_amount) FROM purchases WHERE date = ?");
        if ($purchases_stmt) {
            $purchases_stmt->bind_param("s", $today);
            $purchases_stmt->execute();
            $purchases_stmt->bind_result($today_purchases);
            $purchases_stmt->fetch();
            $purchases_stmt->close();

            // التأكد من أن القيمة ليست null
            $today_purchases = $today_purchases ?? 0;
        } else {
            error_log("خطأ في إعداد استعلام المشتريات: " . $db->error);
        }
    } else {
        error_log("جدول المشتريات غير موجود");
        $today_purchases = 0;
    }
} catch (Exception $e) {
    error_log("استثناء في استعلام المشتريات: " . $e->getMessage());
    $today_purchases = 0;
}

// تنظيف النتائج مرة أخرى
$db = resetDBConnection($db);

// 4. استعلام عدد العملاء
$customers_count = 0;

try {
    $customers_stmt = $db->prepare("SELECT COUNT(id) FROM customers");
    if ($customers_stmt) {
        $customers_stmt->execute();
        $customers_stmt->bind_result($customers_count);
        $customers_stmt->fetch();
        $customers_stmt->close();
    } else {
        error_log("خطأ في إعداد استعلام العملاء: " . $db->error);
    }
} catch (Exception $e) {
    error_log("استثناء في استعلام العملاء: " . $e->getMessage());
}

// استعلام إجمالي المبيعات والمشتريات للشهر الحالي
$current_month = date('Y-m');
$month_start = $current_month . '-01';
$month_end = date('Y-m-t'); // آخر يوم في الشهر الحالي

// إجمالي المبيعات الشهرية
$monthly_sales = 0;
try {
    $db = resetDBConnection($db);
    $stmt = $db->prepare("SELECT SUM(total_amount) FROM sales WHERE date BETWEEN ? AND ?");
    $stmt->bind_param("ss", $month_start, $month_end);
    $stmt->execute();
    $stmt->bind_result($monthly_sales);
    $stmt->fetch();
    $stmt->close();
} catch (Exception $e) {
    error_log("استثناء في استعلام المبيعات الشهرية: " . $e->getMessage());
}

// إجمالي المشتريات الشهرية
$monthly_purchases = 0;
try {
    $db = resetDBConnection($db);
    // التحقق من وجود جدول المشتريات
    $check_purchases_table = $db->query("SHOW TABLES LIKE 'purchases'");
    if ($check_purchases_table && $check_purchases_table->num_rows > 0) {
        $stmt = $db->prepare("SELECT SUM(total_amount) FROM purchases WHERE date BETWEEN ? AND ?");
        if ($stmt) {
            $stmt->bind_param("ss", $month_start, $month_end);
            $stmt->execute();
            $stmt->bind_result($monthly_purchases);
            $stmt->fetch();
            $stmt->close();

            // التأكد من أن القيمة ليست null
            $monthly_purchases = $monthly_purchases ?? 0;
        } else {
            error_log("خطأ في إعداد استعلام المشتريات الشهرية: " . $db->error);
        }
    } else {
        $monthly_purchases = 0;
    }
} catch (Exception $e) {
    error_log("استثناء في استعلام المشتريات الشهرية: " . $e->getMessage());
    $monthly_purchases = 0;
}

// حساب الربح الشهري (المبيعات - المشتريات)
$monthly_profit = ($monthly_sales ?? 0) - ($monthly_purchases ?? 0);

// استعلام عدد الفواتير
$sales_count = 0;
$purchases_count = 0;

try {
    $db = resetDBConnection($db);
    $stmt = $db->prepare("SELECT COUNT(id) FROM sales");
    $stmt->execute();
    $stmt->bind_result($sales_count);
    $stmt->fetch();
    $stmt->close();

    $db = resetDBConnection($db);
    // التحقق من وجود جدول المشتريات
    $check_purchases_table = $db->query("SHOW TABLES LIKE 'purchases'");
    if ($check_purchases_table && $check_purchases_table->num_rows > 0) {
        $stmt = $db->prepare("SELECT COUNT(id) FROM purchases");
        if ($stmt) {
            $stmt->execute();
            $stmt->bind_result($purchases_count);
            $stmt->fetch();
            $stmt->close();
        } else {
            $purchases_count = 0;
        }
    } else {
        $purchases_count = 0;
    }
} catch (Exception $e) {
    error_log("استثناء في استعلام عدد الفواتير: " . $e->getMessage());
}

require_once __DIR__.'/includes/header.php';
displayMessages(); // عرض أي رسائل خطأ أو نجاح
?>

<!-- Welcome Banner -->
<div class="card mb-4 dashboard-card bg-primary text-white">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2 class="mb-1"><?php echo __('welcome'); ?>, <?php echo $_SESSION['username']; ?>!</h2>
                <p class="mb-0"><?php echo __('dashboard_welcome_message'); ?></p>
            </div>
            <div class="col-md-4 text-md-end mt-3 mt-md-0">
                <span class="badge bg-light text-primary"><?php echo date('d M Y'); ?></span>
            </div>
        </div>
    </div>
</div>

<!-- Key Performance Indicators -->
<div class="row mb-4">
    <!-- Today's Sales -->
    <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="card-title mb-0"><?php echo __('today_sales'); ?></h6>
                    <div class="icon-box bg-primary-light rounded-circle p-2">
                        <i class="fas fa-shopping-bag text-primary"></i>
                    </div>
                </div>
                <h3><?php echo __('currency') . ' ' . number_format($today_sales ?? 0, 2); ?></h3>
                <div class="progress" style="height: 5px;">
                    <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo min(100, (($today_sales ?? 0) / 1000) * 100); ?>%"></div>
                </div>
                <i class="fas fa-chart-line bg-icon"></i>
            </div>
        </div>
    </div>

    <!-- Today's Purchases -->
    <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="card-title mb-0"><?php echo __('today_purchases'); ?></h6>
                    <div class="icon-box bg-danger-light rounded-circle p-2">
                        <i class="fas fa-shopping-cart text-danger"></i>
                    </div>
                </div>
                <h3><?php echo __('currency') . ' ' . number_format($today_purchases ?? 0, 2); ?></h3>
                <div class="progress" style="height: 5px;">
                    <div class="progress-bar bg-danger" role="progressbar" style="width: <?php echo min(100, (($today_purchases ?? 0) / 1000) * 100); ?>%"></div>
                </div>
                <i class="fas fa-cart-arrow-down bg-icon"></i>
            </div>
        </div>
    </div>

    <!-- Monthly Profit -->
    <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="card-title mb-0"><?php echo __('monthly_profit'); ?></h6>
                    <div class="icon-box bg-success-light rounded-circle p-2">
                        <i class="fas fa-dollar-sign text-success"></i>
                    </div>
                </div>
                <h3><?php echo __('currency') . ' ' . number_format($monthly_profit, 2); ?></h3>
                <div class="progress" style="height: 5px;">
                    <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $monthly_profit > 0 ? min(100, ($monthly_profit / 5000) * 100) : 0; ?>%"></div>
                </div>
                <i class="fas fa-chart-bar bg-icon"></i>
            </div>
        </div>
    </div>

    <!-- Customers Count -->
    <div class="col-md-3 col-sm-6 mb-4 mb-md-0">
        <div class="card dashboard-card h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="card-title mb-0"><?php echo __('customers'); ?></h6>
                    <div class="icon-box bg-info-light rounded-circle p-2">
                        <i class="fas fa-users text-info"></i>
                    </div>
                </div>
                <h3><?php echo number_format($customers_count); ?></h3>
                <div class="progress" style="height: 5px;">
                    <div class="progress-bar bg-info" role="progressbar" style="width: <?php echo min(100, ($customers_count / 50) * 100); ?>%"></div>
                </div>
                <i class="fas fa-user-friends bg-icon"></i>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions and Tax Summary -->
<div class="row mb-4">
    <!-- Quick Actions Section -->
    <div class="col-md-6">
        <div class="card dashboard-card mb-3">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-bolt"></i> <?php echo __('quick_actions'); ?>
            </div>
            <div class="card-body p-0">
                <div class="row m-0">
                    <div class="col-6 p-0">
                        <a href="add_sale.php" class="btn btn-outline-primary border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-file-invoice" style="font-size: 24px;"></i>
                            <div class="mt-2"><?php echo __('add_sale'); ?></div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="add_purchase.php" class="btn btn-outline-primary border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-shopping-cart" style="font-size: 24px;"></i>
                            <div class="mt-2"><?php echo __('add_purchase'); ?></div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="reports.php" class="btn btn-outline-primary border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-chart-bar" style="font-size: 24px;"></i>
                            <div class="mt-2"><?php echo __('reports'); ?></div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="tax_calculator.php" class="btn btn-outline-primary border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-calculator" style="font-size: 24px;"></i>
                            <div class="mt-2"><?php echo __('tax_calculator'); ?></div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="check_tables.php" class="btn btn-outline-success border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-database" style="font-size: 24px;"></i>
                            <div class="mt-2">فحص الجداول</div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="add_sample_data.php" class="btn btn-outline-info border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-plus-circle" style="font-size: 24px;"></i>
                            <div class="mt-2">بيانات تجريبية</div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="test_system.php" class="btn btn-outline-warning border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-vial" style="font-size: 24px;"></i>
                            <div class="mt-2">اختبار النظام</div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="test_purchase_fix.php" class="btn btn-outline-danger border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-bug" style="font-size: 24px;"></i>
                            <div class="mt-2">اختبار الإصلاح</div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="test_translations.php" class="btn btn-outline-secondary border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-language" style="font-size: 24px;"></i>
                            <div class="mt-2">اختبار الترجمات</div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="test_all_fixes.php" class="btn btn-outline-dark border-0 w-100 py-4 rounded-0">
                            <i class="fas fa-clipboard-check" style="font-size: 24px;"></i>
                            <div class="mt-2">اختبار شامل</div>
                        </a>
                    </div>
                    <div class="col-6 p-0">
                        <a href="test_add_customer_feature.php" class="btn btn-outline-purple border-0 w-100 py-4 rounded-0" style="color: #6f42c1; border-color: #6f42c1;">
                            <i class="fas fa-user-plus" style="font-size: 24px;"></i>
                            <div class="mt-2">اختبار إضافة العميل</div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tax Summary Section -->
    <div class="col-md-6">
        <div class="card dashboard-card mb-3">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-chart-pie"></i> <?php echo __('tax_summary'); ?>
            </div>
            <div class="card-body p-0">
                <div class="row m-0">
                    <!-- Sales Tax -->
                    <div class="col-6 p-0">
                        <div class="bg-light text-dark p-3 h-100">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0"><?php echo __('sales_tax'); ?></h6>
                                <i class="fas fa-file-invoice text-success"></i>
                            </div>
                            <?php
                            try {
                                $db = resetDBConnection($db);
                                $stmt = $db->prepare("SELECT SUM(tax_amount) FROM sales");
                                if ($stmt) {
                                    $stmt->execute();
                                    $stmt->bind_result($total_sales_tax);
                                    $stmt->fetch();
                                    $stmt->close();

                                    // استعلام إجمالي المبيعات
                                    $db = resetDBConnection($db);
                                    $stmt = $db->prepare("SELECT SUM(total_amount) FROM sales");
                                    $stmt->execute();
                                    $stmt->bind_result($total_sales);
                                    $stmt->fetch();
                                    $stmt->close();

                                    echo '<h4 class="text-success">' . __('currency') . ' ' . number_format($total_sales_tax ?? 0, 2) . '</h4>';
                                    echo '<div class="progress" style="height: 5px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: ' . min(100, (($total_sales_tax ?? 0) / 5000) * 100) . '%"></div>
                                    </div>';
                                    echo '<p class="small mt-2">' . __('total_sales') . ': ' . number_format($total_sales ?? 0, 2) . ' ' . __('currency') . '</p>';
                                } else {
                                    echo '<h4 class="text-success">' . __('currency') . ' 0.00</h4>';
                                    echo '<div class="progress" style="height: 5px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                                    </div>';
                                    echo '<p class="small mt-2">' . __('total_sales') . ': 0.00 ' . __('currency') . '</p>';
                                }
                            } catch (Exception $e) {
                                echo '<h4 class="text-success">' . __('currency') . ' 0.00</h4>';
                                echo '<div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                                </div>';
                                echo '<p class="small mt-2">' . __('total_sales') . ': 0.00 ' . __('currency') . '</p>';
                                error_log("Exception in sales tax query: " . $e->getMessage());
                            }
                            ?>
                        </div>
                    </div>

                    <!-- Purchases Tax -->
                    <div class="col-6 p-0">
                        <div class="bg-light text-dark p-3 h-100">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0"><?php echo __('purchases_tax'); ?></h6>
                                <i class="fas fa-shopping-cart text-danger"></i>
                            </div>
                            <?php
                            try {
                                $db = resetDBConnection($db);

                                // التحقق من وجود جدول المشتريات
                                $check_purchases_table = $db->query("SHOW TABLES LIKE 'purchases'");
                                if ($check_purchases_table && $check_purchases_table->num_rows > 0) {
                                    $stmt = $db->prepare("SELECT SUM(tax_amount) FROM purchases");
                                    if ($stmt) {
                                        $stmt->execute();
                                        $stmt->bind_result($total_purchases_tax);
                                        $stmt->fetch();
                                        $stmt->close();

                                        // استعلام إجمالي المشتريات
                                        $db = resetDBConnection($db);
                                        $stmt = $db->prepare("SELECT SUM(total_amount) FROM purchases");
                                        $stmt->execute();
                                        $stmt->bind_result($total_purchases);
                                        $stmt->fetch();
                                        $stmt->close();

                                        echo '<h4 class="text-danger">' . __('currency') . ' ' . number_format($total_purchases_tax ?? 0, 2) . '</h4>';
                                        echo '<div class="progress" style="height: 5px;">
                                            <div class="progress-bar bg-danger" role="progressbar" style="width: ' . min(100, (($total_purchases_tax ?? 0) / 5000) * 100) . '%"></div>
                                        </div>';
                                        echo '<p class="small mt-2">' . __('total_purchases') . ': ' . number_format($total_purchases ?? 0, 2) . ' ' . __('currency') . '</p>';
                                    } else {
                                        echo '<h4 class="text-danger">' . __('currency') . ' 0.00</h4>';
                                        echo '<div class="progress" style="height: 5px;">
                                            <div class="progress-bar bg-danger" role="progressbar" style="width: 0%"></div>
                                        </div>';
                                        echo '<p class="small mt-2">' . __('total_purchases') . ': 0.00 ' . __('currency') . '</p>';
                                    }
                                } else {
                                    echo '<h4 class="text-danger">' . __('currency') . ' 0.00</h4>';
                                    echo '<div class="progress" style="height: 5px;">
                                        <div class="progress-bar bg-danger" role="progressbar" style="width: 0%"></div>
                                    </div>';
                                    echo '<p class="small mt-2">' . __('total_purchases') . ': 0.00 ' . __('currency') . '</p>';
                                }
                            } catch (Exception $e) {
                                echo '<h4 class="text-danger">' . __('currency') . ' 0.00</h4>';
                                echo '<div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-danger" role="progressbar" style="width: 0%"></div>
                                </div>';
                                echo '<p class="small mt-2">' . __('total_purchases') . ': 0.00 ' . __('currency') . '</p>';
                                error_log("Exception in purchases tax query: " . $e->getMessage());
                            }
                            ?>
                        </div>
                    </div>

                    <!-- Tax Due -->
                    <div class="col-12 p-0">
                        <div class="bg-primary text-white p-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0"><?php echo __('tax_due'); ?></h6>
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <?php
                            try {
                                $sales_tax = $total_sales_tax ?? 0;
                                $purchases_tax = $total_purchases_tax ?? 0;
                                $tax_due = $sales_tax - $purchases_tax;
                                $tax_due_class = $tax_due >= 0 ? 'text-white' : 'text-warning';

                                echo '<h3 class="' . $tax_due_class . '">' . __('currency') . ' ' . number_format($tax_due, 2) . '</h3>';
                                echo '<div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-white" role="progressbar" style="width: ' . min(100, (abs($tax_due) / 5000) * 100) . '%"></div>
                                </div>';
                                echo '<p class="small mt-2">
                                    <i class="fas fa-info-circle"></i> ' . __('due_to_tax_authority') . '
                                </p>';
                            } catch (Exception $e) {
                                echo '<h3 class="text-white">' . __('currency') . ' 0.00</h3>';
                                echo '<div class="progress" style="height: 5px;">
                                    <div class="progress-bar bg-white" role="progressbar" style="width: 0%"></div>
                                </div>';
                                echo '<p class="small mt-2">
                                    <i class="fas fa-info-circle"></i> ' . __('due_to_tax_authority') . '
                                </p>';
                                error_log("Exception in tax due calculation: " . $e->getMessage());
                            }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row mt-4">
    <!-- Recent Sales -->
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-file-invoice-dollar me-2"></i>
                    <?php echo __('recent_sales'); ?>
                </div>
                <div class="badge bg-success"><?php echo $sales_count; ?> <?php echo __('total'); ?></div>
            </div>
            <div class="card-body">
                <div class="search-box mb-3">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="form-control search-input" data-table="recent-sales-table" placeholder="<?php echo __('search'); ?>...">
                </div>
                <div class="table-responsive">
                    <table class="table table-hover sortable" id="recent-sales-table">
                        <thead>
                            <tr>
                                <th class="sortable"><?php echo __('invoice_number'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th class="sortable"><?php echo __('date'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th class="sortable"><?php echo __('total'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                $db = resetDBConnection($db);
                                $query = "SELECT id, invoice_number, date, total_amount FROM sales ORDER BY id DESC LIMIT 5";
                                $result = $db->query($query);

                                if ($result && $result->num_rows > 0) {
                                    while ($row = $result->fetch_assoc()):
                                    ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($row['invoice_number']); ?></td>
                                        <td><?php echo htmlspecialchars($row['date']); ?></td>
                                        <td><?php echo number_format($row['total_amount'], 2) . ' ' . __('currency'); ?></td>
                                        <td>
                                            <a href="view_sale.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="<?php echo __('view'); ?>">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="4" class="text-center">' . __('no_data') . '</td></tr>';
                                }
                            } catch (Exception $e) {
                                error_log("Error displaying sales: " . $e->getMessage());
                                echo '<tr><td colspan="4" class="text-center">' . __('error') . '</td></tr>';
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <a href="sales.php" class="btn btn-primary">
                        <i class="fas fa-list me-1"></i> <?php echo __('show_all'); ?>
                    </a>
                    <a href="add_sale.php" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i> <?php echo __('add_sale'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Purchases -->
    <div class="col-md-6">
        <div class="card dashboard-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-shopping-cart me-2"></i>
                    <?php echo __('recent_purchases'); ?>
                </div>
                <div class="badge bg-danger"><?php echo $purchases_count; ?> <?php echo __('total'); ?></div>
            </div>
            <div class="card-body">
                <div class="search-box mb-3">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="form-control search-input" data-table="recent-purchases-table" placeholder="<?php echo __('search'); ?>...">
                </div>
                <div class="table-responsive">
                    <table class="table table-hover sortable" id="recent-purchases-table">
                        <thead>
                            <tr>
                                <th class="sortable"><?php echo __('invoice_number'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th class="sortable"><?php echo __('date'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th class="sortable"><?php echo __('total'); ?> <i class="fas fa-sort ms-1"></i></th>
                                <th><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                $db = resetDBConnection($db);

                                // التحقق من وجود جدول المشتريات
                                $check_purchases_table = $db->query("SHOW TABLES LIKE 'purchases'");
                                if ($check_purchases_table && $check_purchases_table->num_rows > 0) {
                                    // التحقق من وجود عمود customer_id
                                    $check_customer_column = $db->query("SHOW COLUMNS FROM purchases LIKE 'customer_id'");
                                    $has_customer_column = ($check_customer_column && $check_customer_column->num_rows > 0);

                                    if ($has_customer_column) {
                                        $query = "SELECT p.id, p.invoice_number, p.date, p.total_amount, c.name as customer_name
                                                 FROM purchases p
                                                 LEFT JOIN customers c ON p.customer_id = c.id
                                                 ORDER BY p.id DESC LIMIT 5";
                                    } else {
                                        $query = "SELECT id, invoice_number, date, total_amount, 'غير محدد' as customer_name
                                                 FROM purchases ORDER BY id DESC LIMIT 5";
                                    }

                                    $result = $db->query($query);

                                    if ($result && $result->num_rows > 0) {
                                        while ($row = $result->fetch_assoc()):
                                        ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($row['invoice_number']); ?></td>
                                            <td><?php echo htmlspecialchars($row['date']); ?></td>
                                            <td><?php echo number_format($row['total_amount'], 2) . ' ' . __('currency'); ?></td>
                                            <td>
                                                <a href="view_purchase.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="<?php echo __('view'); ?>">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        <?php
                                        endwhile;
                                    } else {
                                        echo '<tr><td colspan="4" class="text-center">' . __('no_data') . '</td></tr>';
                                    }
                                } else {
                                    echo '<tr><td colspan="4" class="text-center">جدول المشتريات غير موجود. <a href="check_tables.php">إنشاء الجداول</a></td></tr>';
                                }
                            } catch (Exception $e) {
                                error_log("Error displaying purchases: " . $e->getMessage());
                                echo '<tr><td colspan="4" class="text-center">' . __('error') . ': ' . htmlspecialchars($e->getMessage()) . '</td></tr>';
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <a href="purchases.php" class="btn btn-primary">
                        <i class="fas fa-list me-1"></i> <?php echo __('show_all'); ?>
                    </a>
                    <a href="add_purchase.php" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i> <?php echo __('add_purchase'); ?>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php';
$db->close(); ?>