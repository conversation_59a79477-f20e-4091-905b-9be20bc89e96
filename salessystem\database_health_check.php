<?php
/**
 * أداة فحص صحة قاعدة البيانات الشاملة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// فحص صحة قاعدة البيانات
$health_issues = [];
$health_score = 100;
$recommendations = [];

// 1. فحص الجداول المطلوبة
$required_tables = ['customers', 'products', 'sales', 'sale_items', 'purchases', 'purchase_items', 'settings'];
$existing_tables = [];

$tables_result = $db->query("SHOW TABLES");
while ($row = $tables_result->fetch_array()) {
    $existing_tables[] = $row[0];
}

$missing_tables = array_diff($required_tables, $existing_tables);
if (!empty($missing_tables)) {
    $health_issues[] = "جداول مفقودة: " . implode(', ', $missing_tables);
    $health_score -= count($missing_tables) * 15;
    $recommendations[] = "استخدم أداة تحديث بنية قاعدة البيانات لإنشاء الجداول المفقودة";
}

// 2. فحص الأعمدة المطلوبة
$required_columns = [
    'customers' => ['id', 'name', 'phone', 'email', 'tax_number', 'address'],
    'products' => ['id', 'name', 'price', 'tax_rate', 'stock_quantity', 'unit', 'barcode'],
    'sales' => ['id', 'customer_id', 'invoice_number', 'date', 'total_amount', 'payment_status'],
    'purchases' => ['id', 'supplier_name', 'invoice_number', 'date', 'total_amount', 'payment_status']
];

$missing_columns = [];
foreach ($required_columns as $table => $columns) {
    if (in_array($table, $existing_tables)) {
        $existing_columns = [];
        $columns_result = $db->query("DESCRIBE `$table`");
        while ($row = $columns_result->fetch_assoc()) {
            $existing_columns[] = $row['Field'];
        }
        
        $table_missing = array_diff($columns, $existing_columns);
        if (!empty($table_missing)) {
            $missing_columns[$table] = $table_missing;
            $health_score -= count($table_missing) * 5;
        }
    }
}

if (!empty($missing_columns)) {
    foreach ($missing_columns as $table => $columns) {
        $health_issues[] = "أعمدة مفقودة في جدول $table: " . implode(', ', $columns);
    }
    $recommendations[] = "استخدم أداة تحديث بنية قاعدة البيانات لإضافة الأعمدة المفقودة";
}

// 3. فحص الفهارس
$missing_indexes = [];
$index_checks = [
    'customers' => ['email', 'phone'],
    'products' => ['barcode'],
    'sales' => ['customer_id', 'date'],
    'purchases' => ['date']
];

foreach ($index_checks as $table => $index_columns) {
    if (in_array($table, $existing_tables)) {
        $existing_indexes = [];
        $indexes_result = $db->query("SHOW INDEX FROM `$table`");
        while ($row = $indexes_result->fetch_assoc()) {
            $existing_indexes[] = $row['Column_name'];
        }
        
        $table_missing_indexes = array_diff($index_columns, $existing_indexes);
        if (!empty($table_missing_indexes)) {
            $missing_indexes[$table] = $table_missing_indexes;
            $health_score -= count($table_missing_indexes) * 3;
        }
    }
}

if (!empty($missing_indexes)) {
    foreach ($missing_indexes as $table => $indexes) {
        $health_issues[] = "فهارس مفقودة في جدول $table: " . implode(', ', $indexes);
    }
    $recommendations[] = "أضف فهارس للأعمدة المهمة لتحسين الأداء";
}

// 4. فحص سلامة البيانات
$data_issues = [];

// فحص البيانات المكررة
foreach (['customers', 'products', 'sales', 'purchases'] as $table) {
    if (in_array($table, $existing_tables)) {
        $unique_column = ($table === 'customers' || $table === 'products') ? 'name' : 'invoice_number';
        
        $duplicate_result = $db->query("
            SELECT $unique_column, COUNT(*) as count 
            FROM `$table` 
            GROUP BY $unique_column 
            HAVING COUNT(*) > 1
        ");
        
        if ($duplicate_result && $duplicate_result->num_rows > 0) {
            $duplicates = $duplicate_result->num_rows;
            $data_issues[] = "بيانات مكررة في جدول $table: $duplicates سجل";
            $health_score -= $duplicates * 2;
        }
    }
}

// فحص البيانات اليتيمة (Orphaned data)
if (in_array('sales', $existing_tables) && in_array('customers', $existing_tables)) {
    $orphaned_sales = $db->query("
        SELECT COUNT(*) as count 
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id 
        WHERE s.customer_id IS NOT NULL AND c.id IS NULL
    ");
    
    if ($orphaned_sales) {
        $orphaned_count = $orphaned_sales->fetch_assoc()['count'];
        if ($orphaned_count > 0) {
            $data_issues[] = "مبيعات يتيمة (بدون عميل): $orphaned_count فاتورة";
            $health_score -= $orphaned_count;
        }
    }
}

// 5. فحص أداء قاعدة البيانات
$performance_issues = [];

// فحص حجم الجداول
foreach ($existing_tables as $table) {
    $size_result = $db->query("
        SELECT 
            ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
            table_rows
        FROM information_schema.TABLES 
        WHERE table_schema = DATABASE() AND table_name = '$table'
    ");
    
    if ($size_result) {
        $size_data = $size_result->fetch_assoc();
        if ($size_data['size_mb'] > 100) {
            $performance_issues[] = "جدول $table كبير الحجم: {$size_data['size_mb']} MB";
            $recommendations[] = "فكر في أرشفة البيانات القديمة من جدول $table";
        }
    }
}

// 6. فحص الإعدادات
$settings_issues = [];
if (in_array('settings', $existing_tables)) {
    $settings_count = $db->query("SELECT COUNT(*) as count FROM settings")->fetch_assoc()['count'];
    if ($settings_count < 10) {
        $settings_issues[] = "عدد قليل من الإعدادات: $settings_count إعداد";
        $health_score -= 10;
        $recommendations[] = "استخدم أداة إضافة البيانات الافتراضية لإضافة الإعدادات المطلوبة";
    }
} else {
    $settings_issues[] = "جدول الإعدادات مفقود";
    $health_score -= 20;
}

// تحديد مستوى الصحة
$health_level = 'خطير';
$health_color = 'danger';

if ($health_score >= 90) {
    $health_level = 'ممتاز';
    $health_color = 'success';
} elseif ($health_score >= 75) {
    $health_level = 'جيد';
    $health_color = 'primary';
} elseif ($health_score >= 60) {
    $health_level = 'متوسط';
    $health_color = 'warning';
} elseif ($health_score >= 40) {
    $health_level = 'ضعيف';
    $health_color = 'danger';
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-<?php echo $health_color; ?> text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-heartbeat"></i>
                        فحص صحة قاعدة البيانات الشامل
                    </h4>
                </div>
                <div class="card-body">
                    <!-- النتيجة العامة -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-<?php echo $health_color; ?> text-white">
                                <div class="card-body text-center">
                                    <h2><?php echo $health_score; ?>%</h2>
                                    <p class="mb-0">نتيجة الصحة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $health_level; ?></h3>
                                    <p class="mb-0">مستوى الصحة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($health_issues); ?></h3>
                                    <p class="mb-0">مشاكل مكتشفة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التبويبات -->
                    <ul class="nav nav-tabs" id="healthTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                <i class="fas fa-chart-pie"></i> نظرة عامة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="structure-tab" data-bs-toggle="tab" data-bs-target="#structure" type="button" role="tab">
                                <i class="fas fa-database"></i> البنية
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="data-tab" data-bs-toggle="tab" data-bs-target="#data" type="button" role="tab">
                                <i class="fas fa-table"></i> البيانات
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="recommendations-tab" data-bs-toggle="tab" data-bs-target="#recommendations" type="button" role="tab">
                                <i class="fas fa-lightbulb"></i> التوصيات
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-4" id="healthTabContent">
                        <!-- تبويب النظرة العامة -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-pie text-primary"></i>
                                تحليل صحة قاعدة البيانات
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">الجداول الموجودة</h6>
                                        </div>
                                        <div class="card-body">
                                            <h3 class="text-success"><?php echo count($existing_tables); ?></h3>
                                            <p>من أصل <?php echo count($required_tables); ?> جدول مطلوب</p>
                                            <div class="progress">
                                                <div class="progress-bar bg-success" style="width: <?php echo (count($existing_tables) / count($required_tables)) * 100; ?>%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">المشاكل المكتشفة</h6>
                                        </div>
                                        <div class="card-body">
                                            <h3 class="text-warning"><?php echo count($health_issues); ?></h3>
                                            <p>مشكلة تحتاج إصلاح</p>
                                            <?php if (!empty($health_issues)): ?>
                                            <ul class="list-unstyled">
                                                <?php foreach (array_slice($health_issues, 0, 3) as $issue): ?>
                                                <li><i class="fas fa-exclamation-triangle text-warning"></i> <?php echo $issue; ?></li>
                                                <?php endforeach; ?>
                                                <?php if (count($health_issues) > 3): ?>
                                                <li><small>و <?php echo count($health_issues) - 3; ?> مشاكل أخرى...</small></li>
                                                <?php endif; ?>
                                            </ul>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($health_score < 70): ?>
                            <div class="alert alert-danger mt-4">
                                <h6><i class="fas fa-exclamation-triangle"></i> تحذير</h6>
                                <p>قاعدة البيانات تحتاج إلى إصلاحات عاجلة. يُنصح بتطبيق الإصلاحات المقترحة فوراً.</p>
                            </div>
                            <?php elseif ($health_score < 85): ?>
                            <div class="alert alert-warning mt-4">
                                <h6><i class="fas fa-info-circle"></i> تحسينات مطلوبة</h6>
                                <p>قاعدة البيانات تعمل بشكل جيد ولكن تحتاج بعض التحسينات.</p>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-success mt-4">
                                <h6><i class="fas fa-check-circle"></i> ممتاز!</h6>
                                <p>قاعدة البيانات في حالة ممتازة وتعمل بكفاءة عالية.</p>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب البنية -->
                        <div class="tab-pane fade" id="structure" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-database text-info"></i>
                                تحليل بنية قاعدة البيانات
                            </h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">الجداول</h6>
                                        </div>
                                        <div class="card-body">
                                            <h6 class="text-success">موجودة (<?php echo count($existing_tables); ?>):</h6>
                                            <div class="d-flex flex-wrap">
                                                <?php foreach ($existing_tables as $table): ?>
                                                <span class="badge bg-success me-1 mb-1"><?php echo $table; ?></span>
                                                <?php endforeach; ?>
                                            </div>

                                            <?php if (!empty($missing_tables)): ?>
                                            <h6 class="text-danger mt-3">مفقودة (<?php echo count($missing_tables); ?>):</h6>
                                            <div class="d-flex flex-wrap">
                                                <?php foreach ($missing_tables as $table): ?>
                                                <span class="badge bg-danger me-1 mb-1"><?php echo $table; ?></span>
                                                <?php endforeach; ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">الأعمدة المفقودة</h6>
                                        </div>
                                        <div class="card-body">
                                            <?php if (empty($missing_columns)): ?>
                                            <p class="text-success"><i class="fas fa-check"></i> جميع الأعمدة المطلوبة موجودة</p>
                                            <?php else: ?>
                                            <?php foreach ($missing_columns as $table => $columns): ?>
                                            <h6 class="text-warning"><?php echo $table; ?>:</h6>
                                            <div class="d-flex flex-wrap mb-2">
                                                <?php foreach ($columns as $column): ?>
                                                <span class="badge bg-warning me-1 mb-1"><?php echo $column; ?></span>
                                                <?php endforeach; ?>
                                            </div>
                                            <?php endforeach; ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <?php if (!empty($missing_indexes)): ?>
                            <div class="card mt-3">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">الفهارس المفقودة</h6>
                                </div>
                                <div class="card-body">
                                    <?php foreach ($missing_indexes as $table => $indexes): ?>
                                    <h6 class="text-secondary"><?php echo $table; ?>:</h6>
                                    <div class="d-flex flex-wrap mb-2">
                                        <?php foreach ($indexes as $index): ?>
                                        <span class="badge bg-secondary me-1 mb-1"><?php echo $index; ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب البيانات -->
                        <div class="tab-pane fade" id="data" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-table text-warning"></i>
                                تحليل سلامة البيانات
                            </h5>

                            <?php if (empty($data_issues) && empty($performance_issues) && empty($settings_issues)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                البيانات سليمة ولا توجد مشاكل مكتشفة
                            </div>
                            <?php else: ?>

                            <?php if (!empty($data_issues)): ?>
                            <div class="card mb-3">
                                <div class="card-header bg-danger text-white">
                                    <h6 class="mb-0">مشاكل البيانات</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <?php foreach ($data_issues as $issue): ?>
                                        <li><i class="fas fa-exclamation-triangle text-danger"></i> <?php echo $issue; ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($performance_issues)): ?>
                            <div class="card mb-3">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">مشاكل الأداء</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <?php foreach ($performance_issues as $issue): ?>
                                        <li><i class="fas fa-exclamation-triangle text-warning"></i> <?php echo $issue; ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($settings_issues)): ?>
                            <div class="card mb-3">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">مشاكل الإعدادات</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <?php foreach ($settings_issues as $issue): ?>
                                        <li><i class="fas fa-info-circle text-info"></i> <?php echo $issue; ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php endif; ?>

                            <!-- إحصائيات الجداول -->
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">إحصائيات الجداول</h6>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>الجدول</th>
                                                    <th>عدد السجلات</th>
                                                    <th>الحجم (MB)</th>
                                                    <th>الحالة</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($existing_tables as $table): ?>
                                                <?php
                                                $count_result = $db->query("SELECT COUNT(*) as count FROM `$table`");
                                                $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;

                                                $size_result = $db->query("
                                                    SELECT ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                                                    FROM information_schema.TABLES
                                                    WHERE table_schema = DATABASE() AND table_name = '$table'
                                                ");
                                                $size = $size_result ? $size_result->fetch_assoc()['size_mb'] : 0;
                                                ?>
                                                <tr>
                                                    <td><?php echo $table; ?></td>
                                                    <td><?php echo number_format($count); ?></td>
                                                    <td><?php echo $size; ?></td>
                                                    <td>
                                                        <?php if ($size > 100): ?>
                                                            <span class="badge bg-warning">كبير</span>
                                                        <?php elseif ($count > 10000): ?>
                                                            <span class="badge bg-info">مكتظ</span>
                                                        <?php else: ?>
                                                            <span class="badge bg-success">طبيعي</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب التوصيات -->
                        <div class="tab-pane fade" id="recommendations" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-lightbulb text-success"></i>
                                التوصيات والإصلاحات
                            </h5>

                            <?php if (empty($recommendations)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                لا توجد توصيات. قاعدة البيانات في حالة ممتازة!
                            </div>
                            <?php else: ?>
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">الإجراءات الموصى بها</h6>
                                </div>
                                <div class="card-body">
                                    <ol>
                                        <?php foreach ($recommendations as $recommendation): ?>
                                        <li class="mb-2"><?php echo $recommendation; ?></li>
                                        <?php endforeach; ?>
                                    </ol>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="mt-4">
                                <h6>أدوات الإصلاح المتاحة:</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <a href="update_database_schema.php" class="btn btn-primary w-100" target="_blank">
                                            <i class="fas fa-database"></i> تحديث بنية قاعدة البيانات
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <a href="initialize_default_data.php" class="btn btn-success w-100" target="_blank">
                                            <i class="fas fa-seedling"></i> إضافة البيانات الافتراضية
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <a href="fix_mysqli_sync_errors.php" class="btn btn-warning w-100" target="_blank">
                                            <i class="fas fa-sync-alt"></i> إصلاح مشاكل MySQLi
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <a href="system_tools.php" class="btn btn-secondary w-100">
                                            <i class="fas fa-tools"></i> جميع أدوات النظام
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> نصائح للصيانة</h6>
                                    <ul class="mb-0">
                                        <li>قم بفحص صحة قاعدة البيانات دورياً (شهرياً على الأقل)</li>
                                        <li>احتفظ بنسخ احتياطية منتظمة من قاعدة البيانات</li>
                                        <li>راقب حجم الجداول وأرشف البيانات القديمة عند الحاجة</li>
                                        <li>تأكد من وجود فهارس مناسبة للاستعلامات المتكررة</li>
                                        <li>نظف البيانات المكررة والمعطوبة بانتظام</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
