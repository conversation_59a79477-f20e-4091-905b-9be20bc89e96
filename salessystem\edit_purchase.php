<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';  // هذا الملف يحتوي على displayMessages()
require_once __DIR__.'/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();

if (!isset($_GET['id'])) {
    header("Location: purchases.php");
    exit();
}

$purchase_id = intval($_GET['id']);

// التحقق من وجود جدول المشتريات
$check_table = $db->query("SHOW TABLES LIKE 'purchases'");
if (!$check_table || $check_table->num_rows == 0) {
    $_SESSION['error'] = "جدول المشتريات غير موجود. يرجى تشغيل فحص الجداول أولاً.";
    header("Location: check_tables.php");
    exit();
}

// التحقق من وجود الأعمدة المطلوبة
$required_columns = ['id', 'invoice_number', 'date'];
$missing_columns = [];

foreach ($required_columns as $column) {
    $check_column = $db->query("SHOW COLUMNS FROM purchases LIKE '$column'");
    if (!$check_column || $check_column->num_rows == 0) {
        $missing_columns[] = $column;
    }
}

if (!empty($missing_columns)) {
    $_SESSION['error'] = "بعض الأعمدة المطلوبة غير موجودة في جدول المشتريات: " . implode(', ', $missing_columns) . ". يرجى تشغيل فحص الجداول أولاً.";
    header("Location: check_tables.php");
    exit();
}

// جلب بيانات الفاتورة
$stmt = $db->prepare("SELECT * FROM purchases WHERE id = ?");
$stmt->bind_param("i", $purchase_id);
$stmt->execute();
$result = $stmt->get_result();
$purchase = $result->fetch_assoc();

if (!$purchase) {
    $_SESSION['error'] = "فاتورة المشتريات غير موجودة";
    header("Location: purchases.php");
    exit();
}

// جلب عناصر الفاتورة
$items_result = $db->query("SELECT * FROM purchase_items WHERE purchase_id = $purchase_id");
$items = [];
while ($item = $items_result->fetch_assoc()) {
    $items[] = $item;
}

// جلب قائمة المنتجات والعملاء
$products = $db->query("SELECT id, name, price, tax_rate FROM products ORDER BY name");
$customers = $db->query("SELECT id, name FROM customers ORDER BY name");

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $customer_id = !empty($_POST['customer_id']) ? intval($_POST['customer_id']) : NULL;
    $date = $_POST['date'];
    $notes = trim($_POST['notes']);

    // معالجة العناصر
    $new_items = [];
    $product_ids = $_POST['product_id'] ?? [];
    $quantities = $_POST['quantity'] ?? [];
    $prices = $_POST['price'] ?? [];
    $tax_rates = $_POST['tax_rate'] ?? [];

    for ($i = 0; $i < count($product_ids); $i++) {
        if (!empty($product_ids[$i]) && !empty($quantities[$i])) {
            $new_items[] = [
                'product_id' => intval($product_ids[$i]),
                'quantity' => intval($quantities[$i]),
                'unit_price' => floatval($prices[$i]),
                'tax_rate' => floatval($tax_rates[$i])
            ];
        }
    }

    if (empty($new_items)) {
        $_SESSION['error'] = "يجب إضافة عنصر واحد على الأقل";
    } else {
        // حساب المجموع والضريبة
        $calculations = calculateTaxAndTotal($new_items);

        // التحقق من وجود عمود customer_id في جدول purchases
        $check_column = $db->query("SHOW COLUMNS FROM purchases LIKE 'customer_id'");
        $has_customer_id = ($check_column && $check_column->num_rows > 0);

        // تحديث الفاتورة
        if ($has_customer_id) {
            // إذا كان العمود موجودًا، استخدمه في الاستعلام
            $stmt = $db->prepare("UPDATE purchases SET
                                customer_id = ?,
                                date = ?,
                                subtotal = ?,
                                tax_amount = ?,
                                total_amount = ?,
                                notes = ?
                                WHERE id = ?");
            $stmt->bind_param("isdddsi", $customer_id, $date,
                            $calculations['subtotal'], $calculations['tax_amount'],
                            $calculations['total'], $notes, $purchase_id);
        } else {
            // إذا لم يكن العمود موجودًا، استخدم استعلامًا بدون العمود
            $stmt = $db->prepare("UPDATE purchases SET
                                date = ?,
                                subtotal = ?,
                                tax_amount = ?,
                                total_amount = ?,
                                notes = ?
                                WHERE id = ?");
            $stmt->bind_param("sdddsi", $date,
                            $calculations['subtotal'], $calculations['tax_amount'],
                            $calculations['total'], $notes, $purchase_id);
        }

        if ($stmt->execute()) {
            // حذف العناصر القديمة
            $db->query("DELETE FROM purchase_items WHERE purchase_id = $purchase_id");

            // إضافة العناصر الجديدة
            $item_stmt = $db->prepare("INSERT INTO purchase_items
                                     (purchase_id, product_id, product_name, quantity, unit_price, tax_rate, tax_amount, total_price)
                                     VALUES (?, ?, ?, ?, ?, ?, ?, ?)");

            foreach ($new_items as $item) {
                $product_name = '';
                foreach ($products as $product) {
                    if ($product['id'] == $item['product_id']) {
                        $product_name = $product['name'];
                        break;
                    }
                }

                $item_total = $item['quantity'] * $item['unit_price'];
                $item_tax = $item_total * ($item['tax_rate'] / 100);
                $total_price = $item_total + $item_tax;

                $item_stmt->bind_param("iisidddd", $purchase_id, $item['product_id'], $product_name,
                                      $item['quantity'], $item['unit_price'], $item['tax_rate'],
                                      $item_tax, $total_price);
                $item_stmt->execute();
            }

            $_SESSION['success'] = "تم تحديث فاتورة المشتريات بنجاح";
            header("Location: purchases.php");
            exit();
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء تحديث فاتورة المشتريات";
        }
    }
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">تعديل فاتورة مشتريات</div>
            <div class="card-body">
                <form method="POST" id="purchaseForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="customer_id" class="form-label">العميل</label>
                                <?php
                                // التحقق من وجود عمود customer_id في جدول purchases
                                $check_customer_column = $db->query("SHOW COLUMNS FROM purchases LIKE 'customer_id'");
                                $has_customer_column = ($check_customer_column && $check_customer_column->num_rows > 0);

                                if ($has_customer_column):
                                ?>
                                <select class="form-select" id="customer_id" name="customer_id">
                                    <option value="">-- اختر عميل --</option>
                                    <?php while ($customer = $customers->fetch_assoc()): ?>
                                    <option value="<?php echo $customer['id']; ?>" <?php echo (isset($purchase['customer_id']) && $purchase['customer_id'] == $customer['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($customer['name']); ?>
                                    </option>
                                    <?php endwhile; ?>
                                </select>
                                <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle"></i> عمود العميل غير موجود في قاعدة البيانات. يرجى تشغيل <a href="check_tables.php" class="alert-link">فحص الجداول</a> لإصلاح المشكلة.
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date" class="form-label"><?php echo __('invoice_date'); ?></label>
                                <input type="date" class="form-control" id="date" name="date" value="<?php echo $purchase['date']; ?>" required>
                            </div>
                        </div>
                    </div>

                    <h5 class="mt-4">عناصر الفاتورة</h5>
                    <div class="table-responsive">
                        <table class="table" id="itemsTable">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الضريبة %</th>
                                    <th>المجموع</th>
                                    <th>إجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="itemsBody">
                                <!-- سيتم إضافة العناصر ديناميكيًا هنا -->
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="6">
                                        <button type="button" class="btn btn-sm btn-success" id="addItemBtn">
                                            <i class="fas fa-plus"></i> إضافة عنصر
                                        </button>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="notes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($purchase['notes']); ?></textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">ملخص الفاتورة</div>
                                <div class="card-body">
                                    <table class="table">
                                        <tr>
                                            <th>المجموع الفرعي:</th>
                                            <td id="subtotalCell"><?php echo number_format(isset($purchase['subtotal']) ? $purchase['subtotal'] : 0, 2); ?> ر.س</td>
                                        </tr>
                                        <tr>
                                            <th>الضريبة:</th>
                                            <td id="taxCell"><?php echo number_format(isset($purchase['tax_amount']) ? $purchase['tax_amount'] : 0, 2); ?> ر.س</td>
                                        </tr>
                                        <tr>
                                            <th>الإجمالي:</th>
                                            <td id="totalCell"><?php echo number_format(isset($purchase['total_amount']) ? $purchase['total_amount'] : 0, 2); ?> ر.س</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                        <a href="purchases.php" class="btn btn-secondary">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// متغير لتخزين بيانات المنتجات
const products = [
    <?php
    while ($product = $products->fetch_assoc()):
        echo "{id: {$product['id']}, name: '" . addslashes($product['name']) . "', price: {$product['price']}, tax_rate: {$product['tax_rate']}},";
    endwhile;
    ?>
];

// دالة لإضافة صف عنصر جديد
function addItemRow(item = null) {
    const tbody = document.getElementById('itemsBody');
    const rowId = Date.now();

    const row = document.createElement('tr');
    row.id = `row_${rowId}`;

    // عمود المنتج
    const productCell = document.createElement('td');
    const productSelect = document.createElement('select');
    productSelect.className = 'form-select product-select';
    productSelect.name = 'product_id[]';
    productSelect.required = true;

    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = '-- اختر منتج --';
    productSelect.appendChild(defaultOption);

    products.forEach(p => {
        const option = document.createElement('option');
        option.value = p.id;
        option.textContent = p.name;
        option.dataset.price = p.price;
        option.dataset.taxRate = p.tax_rate;

        if (item && item.product_id == p.id) {
            option.selected = true;
        }

        productSelect.appendChild(option);
    });

    productSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const price = selectedOption.dataset.price || 0;
        const taxRate = selectedOption.dataset.taxRate || 0;

        const row = this.closest('tr');
        row.querySelector('.price-input').value = price;
        row.querySelector('.tax-rate-input').value = taxRate;

        calculateRowTotal(row);
        updateInvoiceSummary();
    });

    productCell.appendChild(productSelect);
    row.appendChild(productCell);

    // عمود الكمية
    const quantityCell = document.createElement('td');
    const quantityInput = document.createElement('input');
    quantityInput.type = 'number';
    quantityInput.className = 'form-control quantity-input';
    quantityInput.name = 'quantity[]';
    quantityInput.min = '1';
    quantityInput.value = item ? item.quantity : '1';
    quantityInput.required = true;
    quantityInput.addEventListener('input', function() {
        calculateRowTotal(this.closest('tr'));
        updateInvoiceSummary();
    });
    quantityCell.appendChild(quantityInput);
    row.appendChild(quantityCell);

    // عمود السعر
    const priceCell = document.createElement('td');
    const priceInput = document.createElement('input');
    priceInput.type = 'number';
    priceInput.className = 'form-control price-input';
    priceInput.name = 'price[]';
    priceInput.step = '0.01';
    priceInput.min = '0';
    priceInput.value = item ? item.unit_price : '0';
    priceInput.required = true;
    priceInput.addEventListener('input', function() {
        calculateRowTotal(this.closest('tr'));
        updateInvoiceSummary();
    });
    priceCell.appendChild(priceInput);
    row.appendChild(priceCell);

    // عمود الضريبة
    const taxCell = document.createElement('td');
    const taxInput = document.createElement('input');
    taxInput.type = 'number';
    taxInput.className = 'form-control tax-rate-input';
    taxInput.name = 'tax_rate[]';
    taxInput.step = '0.01';
    taxInput.min = '0';
    taxInput.value = item ? item.tax_rate : '0';
    taxInput.required = true;
    taxInput.addEventListener('input', function() {
        calculateRowTotal(this.closest('tr'));
        updateInvoiceSummary();
    });
    taxCell.appendChild(taxInput);
    row.appendChild(taxCell);

    // عمود المجموع
    const totalCell = document.createElement('td');
    totalCell.className = 'row-total';
    totalCell.textContent = item ? (item.total_price).toFixed(2) + ' ر.س' : '0.00 ر.س';
    row.appendChild(totalCell);

    // عمود الإجراءات
    const actionsCell = document.createElement('td');
    const deleteBtn = document.createElement('button');
    deleteBtn.type = 'button';
    deleteBtn.className = 'btn btn-sm btn-danger';
    deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
    deleteBtn.addEventListener('click', function() {
        row.remove();
        updateInvoiceSummary();
    });
    actionsCell.appendChild(deleteBtn);
    row.appendChild(actionsCell);

    tbody.appendChild(row);

    // إذا تم تمرير عنصر، احسب المجموع للصف
    if (item) {
        calculateRowTotal(row);
    }

    return row;
}

// دالة لحساب مجموع الصف
function calculateRowTotal(row) {
    const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
    const price = parseFloat(row.querySelector('.price-input').value) || 0;
    const taxRate = parseFloat(row.querySelector('.tax-rate-input').value) || 0;

    const subtotal = quantity * price;
    const taxAmount = subtotal * (taxRate / 100);
    const total = subtotal + taxAmount;

    row.querySelector('.row-total').textContent = total.toFixed(2) + ' ر.س';
}

// دالة لتحديث ملخص الفاتورة
function updateInvoiceSummary() {
    let subtotal = 0;
    let taxAmount = 0;

    document.querySelectorAll('#itemsBody tr').forEach(row => {
        const quantity = parseFloat(row.querySelector('.quantity-input').value) || 0;
        const price = parseFloat(row.querySelector('.price-input').value) || 0;
        const taxRate = parseFloat(row.querySelector('.tax-rate-input').value) || 0;

        const rowSubtotal = quantity * price;
        const rowTax = rowSubtotal * (taxRate / 100);

        subtotal += rowSubtotal;
        taxAmount += rowTax;
    });

    const total = subtotal + taxAmount;

    document.getElementById('subtotalCell').textContent = subtotal.toFixed(2) + ' ر.س';
    document.getElementById('taxCell').textContent = taxAmount.toFixed(2) + ' ر.س';
    document.getElementById('totalCell').textContent = total.toFixed(2) + ' ر.س';
}

// إضافة العناصر الموجودة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    <?php foreach ($items as $item): ?>
        addItemRow({
            product_id: <?php echo $item['product_id']; ?>,
            quantity: <?php echo $item['quantity']; ?>,
            unit_price: <?php echo $item['unit_price']; ?>,
            tax_rate: <?php echo $item['tax_rate']; ?>,
            total_price: <?php echo $item['total_price']; ?>
        });
    <?php endforeach; ?>

    // إضافة صف فارغ إذا لم يكن هناك عناصر
    if (document.querySelectorAll('#itemsBody tr').length === 0) {
        addItemRow();
    }

    // معالجة زر إضافة عنصر
    document.getElementById('addItemBtn').addEventListener('click', function() {
        addItemRow();
    });

    // تحديث الملخص عند أي تغيير
    document.getElementById('itemsBody').addEventListener('input', function() {
        updateInvoiceSummary();
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>