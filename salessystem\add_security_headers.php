<?php
/**
 * أداة إضافة Security Headers
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// معالجة طلب إضافة Security Headers
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_security_headers'])) {
    $files_updated = 0;
    $errors = [];
    
    try {
        // 1. إنشاء ملف Security Headers helper
        $security_headers_content = '<?php
/**
 * مساعد Security Headers
 */

class SecurityHeaders {
    
    /**
     * إضافة جميع Security Headers
     */
    public static function setAllHeaders() {
        // منع تشغيل الصفحة في iframe (Clickjacking protection)
        self::setFrameOptions();
        
        // منع MIME type sniffing
        self::setContentTypeOptions();
        
        // تفعيل XSS protection في المتصفح
        self::setXSSProtection();
        
        // إجبار استخدام HTTPS
        self::setStrictTransportSecurity();
        
        // Content Security Policy
        self::setContentSecurityPolicy();
        
        // Referrer Policy
        self::setReferrerPolicy();
        
        // Feature Policy
        self::setFeaturePolicy();
        
        // Remove server information
        self::removeServerInfo();
    }
    
    /**
     * X-Frame-Options header
     */
    public static function setFrameOptions($option = "DENY") {
        header("X-Frame-Options: " . $option);
    }
    
    /**
     * X-Content-Type-Options header
     */
    public static function setContentTypeOptions() {
        header("X-Content-Type-Options: nosniff");
    }
    
    /**
     * X-XSS-Protection header
     */
    public static function setXSSProtection() {
        header("X-XSS-Protection: 1; mode=block");
    }
    
    /**
     * Strict-Transport-Security header
     */
    public static function setStrictTransportSecurity($maxAge = 31536000) {
        if (isset($_SERVER["HTTPS"]) && $_SERVER["HTTPS"] === "on") {
            header("Strict-Transport-Security: max-age=" . $maxAge . "; includeSubDomains; preload");
        }
    }
    
    /**
     * Content-Security-Policy header
     */
    public static function setContentSecurityPolicy() {
        $csp = "default-src \'self\'; " .
               "script-src \'self\' \'unsafe-inline\' \'unsafe-eval\' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
               "style-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; " .
               "font-src \'self\' https://fonts.gstatic.com https://cdn.jsdelivr.net; " .
               "img-src \'self\' data: https:; " .
               "connect-src \'self\'; " .
               "frame-src \'none\'; " .
               "object-src \'none\'; " .
               "base-uri \'self\'; " .
               "form-action \'self\'; " .
               "frame-ancestors \'none\';";
        
        header("Content-Security-Policy: " . $csp);
    }
    
    /**
     * Referrer-Policy header
     */
    public static function setReferrerPolicy() {
        header("Referrer-Policy: strict-origin-when-cross-origin");
    }
    
    /**
     * Feature-Policy header
     */
    public static function setFeaturePolicy() {
        $policy = "camera \'none\'; " .
                  "microphone \'none\'; " .
                  "geolocation \'none\'; " .
                  "payment \'none\'; " .
                  "usb \'none\'; " .
                  "magnetometer \'none\'; " .
                  "accelerometer \'none\'; " .
                  "gyroscope \'none\';";
        
        header("Feature-Policy: " . $policy);
        header("Permissions-Policy: " . str_replace("\'none\'", "=()", $policy));
    }
    
    /**
     * إزالة معلومات الخادم
     */
    public static function removeServerInfo() {
        header_remove("X-Powered-By");
        header_remove("Server");
    }
    
    /**
     * إضافة headers للملفات الثابتة
     */
    public static function setStaticFileHeaders() {
        // Cache control للملفات الثابتة
        $extension = pathinfo($_SERVER["REQUEST_URI"], PATHINFO_EXTENSION);
        
        switch ($extension) {
            case "css":
            case "js":
                header("Cache-Control: public, max-age=31536000");
                break;
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "ico":
                header("Cache-Control: public, max-age=2592000");
                break;
        }
    }
    
    /**
     * إضافة headers للـ API responses
     */
    public static function setAPIHeaders() {
        header("Content-Type: application/json; charset=utf-8");
        header("Cache-Control: no-cache, no-store, must-revalidate");
        header("Pragma: no-cache");
        header("Expires: 0");
    }
}
';
        
        $security_headers_file = __DIR__ . '/includes/security_headers.php';
        if (file_put_contents($security_headers_file, $security_headers_content)) {
            $files_updated++;
        } else {
            $errors[] = "فشل في إنشاء ملف security_headers.php";
        }
        
        // 2. تحديث ملف header.php لإضافة Security Headers
        $header_file = __DIR__ . '/includes/header.php';
        if (file_exists($header_file)) {
            $content = file_get_contents($header_file);
            $original_content = $content;
            
            // إضافة require للـ security headers
            if (strpos($content, 'security_headers.php') === false) {
                $security_require = '<?php
require_once __DIR__ . \'/security_headers.php\';
SecurityHeaders::setAllHeaders();
?>';
                
                $content = $security_require . PHP_EOL . $content;
            }
            
            if ($content !== $original_content) {
                if (file_put_contents($header_file, $content)) {
                    $files_updated++;
                } else {
                    $errors[] = "فشل في تحديث ملف header.php";
                }
            }
        }
        
        // 3. تحديث ملف ajax_handler.php لإضافة API headers
        $ajax_file = __DIR__ . '/ajax_handler.php';
        if (file_exists($ajax_file)) {
            $content = file_get_contents($ajax_file);
            $original_content = $content;
            
            // إضافة require للـ security headers
            if (strpos($content, 'security_headers.php') === false) {
                $content = str_replace(
                    'require_once __DIR__ . \'/includes/functions.php\';',
                    'require_once __DIR__ . \'/includes/functions.php\';' . PHP_EOL . 'require_once __DIR__ . \'/includes/security_headers.php\';',
                    $content
                );
            }
            
            // إضافة API headers
            if (strpos($content, 'SecurityHeaders::setAPIHeaders') === false) {
                $content = str_replace(
                    'header(\'Content-Type: application/json\');',
                    'SecurityHeaders::setAPIHeaders();',
                    $content
                );
            }
            
            if ($content !== $original_content) {
                if (file_put_contents($ajax_file, $content)) {
                    $files_updated++;
                } else {
                    $errors[] = "فشل في تحديث ملف ajax_handler.php";
                }
            }
        }
        
        // 4. إنشاء ملف .htaccess محدث مع Security Headers
        $htaccess_content = '# Security Headers
<IfModule mod_headers.c>
    # X-Frame-Options
    Header always set X-Frame-Options "DENY"
    
    # X-Content-Type-Options
    Header always set X-Content-Type-Options "nosniff"
    
    # X-XSS-Protection
    Header always set X-XSS-Protection "1; mode=block"
    
    # Strict-Transport-Security (only over HTTPS)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" env=HTTPS
    
    # Content-Security-Policy
    Header always set Content-Security-Policy "default-src \'self\'; script-src \'self\' \'unsafe-inline\' \'unsafe-eval\' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; font-src \'self\' https://fonts.gstatic.com https://cdn.jsdelivr.net; img-src \'self\' data: https:; connect-src \'self\'; frame-src \'none\'; object-src \'none\'; base-uri \'self\'; form-action \'self\'; frame-ancestors \'none\';"
    
    # Referrer-Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Feature-Policy / Permissions-Policy
    Header always set Feature-Policy "camera \'none\'; microphone \'none\'; geolocation \'none\'; payment \'none\'; usb \'none\';"
    Header always set Permissions-Policy "camera=(), microphone=(), geolocation=(), payment=(), usb=()"
    
    # Remove server information
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# إعدادات أمان إضافية
<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # منع الوصول للملفات الحساسة
    RewriteRule ^(config|includes|logs)/ - [F,L]
    RewriteRule \.(log|sql|md)$ - [F,L]
    
    # إجبار HTTPS (إذا كان متاحاً)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>

# منع الوصول للملفات الحساسة
<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

# إعدادات PHP الأمنية
<IfModule mod_php7.c>
    php_value expose_php Off
    php_value display_errors Off
    php_value log_errors On
    php_value session.cookie_httponly 1
    php_value session.cookie_secure 1
    php_value session.use_strict_mode 1
</IfModule>

# Cache Control للملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
</IfModule>
';
        
        $htaccess_file = __DIR__ . '/.htaccess';
        if (file_put_contents($htaccess_file, $htaccess_content)) {
            $files_updated++;
        } else {
            $errors[] = "فشل في إنشاء ملف .htaccess";
        }
        
        // 5. إنشاء ملف robots.txt للأمان
        $robots_content = 'User-agent: *
Disallow: /config/
Disallow: /includes/
Disallow: /logs/
Disallow: *.log
Disallow: *.sql
Disallow: *.md
Disallow: /system_tools.php
Disallow: /security_audit.php
Disallow: /fix_*.php
Disallow: /add_*.php
Disallow: /test_*.php
Disallow: /debug_*.php

# Allow access to assets
Allow: /assets/

Sitemap: ' . (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . '/sitemap.xml
';
        
        $robots_file = __DIR__ . '/robots.txt';
        if (file_put_contents($robots_file, $robots_content)) {
            $files_updated++;
        } else {
            $errors[] = "فشل في إنشاء ملف robots.txt";
        }
        
        if ($files_updated > 0) {
            $_SESSION['success'] = "تم تحديث $files_updated ملف بـ Security Headers";
        } else {
            $_SESSION['info'] = "لا توجد ملفات تحتاج لتحديث";
        }
        
        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', $errors);
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء إضافة Security Headers: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-code"></i>
                        إضافة Security Headers
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> حول Security Headers</h6>
                        <p>Security Headers هي مجموعة من الهيدرز التي تخبر المتصفح كيفية التعامل مع المحتوى بطريقة آمنة.</p>
                        <p>هذه الأداة ستقوم بإضافة جميع Security Headers المهمة لحماية الموقع من الهجمات الشائعة.</p>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">Headers المضافة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-shield-alt text-success"></i> X-Frame-Options</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> X-Content-Type-Options</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> X-XSS-Protection</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> Strict-Transport-Security</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> Content-Security-Policy</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> Referrer-Policy</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">الحماية المضافة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> منع Clickjacking</li>
                                        <li><i class="fas fa-check text-success"></i> منع MIME sniffing</li>
                                        <li><i class="fas fa-check text-success"></i> حماية XSS</li>
                                        <li><i class="fas fa-check text-success"></i> إجبار HTTPS</li>
                                        <li><i class="fas fa-check text-success"></i> تحكم في المحتوى</li>
                                        <li><i class="fas fa-check text-success"></i> حماية الخصوصية</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من إضافة Security Headers؟')">
                        <div class="text-center">
                            <button type="submit" name="add_security_headers" class="btn btn-warning btn-lg">
                                <i class="fas fa-code"></i> إضافة Security Headers
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4">
                        <h6>تفاصيل Security Headers:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Header</th>
                                        <th>الوظيفة</th>
                                        <th>القيمة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>X-Frame-Options</code></td>
                                        <td>منع تشغيل الصفحة في iframe</td>
                                        <td>DENY</td>
                                    </tr>
                                    <tr>
                                        <td><code>X-Content-Type-Options</code></td>
                                        <td>منع MIME type sniffing</td>
                                        <td>nosniff</td>
                                    </tr>
                                    <tr>
                                        <td><code>X-XSS-Protection</code></td>
                                        <td>تفعيل حماية XSS في المتصفح</td>
                                        <td>1; mode=block</td>
                                    </tr>
                                    <tr>
                                        <td><code>Strict-Transport-Security</code></td>
                                        <td>إجبار استخدام HTTPS</td>
                                        <td>max-age=31536000</td>
                                    </tr>
                                    <tr>
                                        <td><code>Content-Security-Policy</code></td>
                                        <td>تحكم في مصادر المحتوى</td>
                                        <td>default-src 'self'</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
