<?php
/**
 * أداة شاملة لفحص وترجمة جميع النصوص العربية غير المترجمة في المشروع
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// معالجة طلب الترجمة الشاملة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['comprehensive_translate'])) {
    $files_updated = 0;
    $translations_added = 0;
    $replacements_made = 0;
    $errors = [];

    try {
        // قائمة شاملة لجميع النصوص العربية المكتشفة في المشروع
        $comprehensive_arabic_texts = [
            // النصوص من التعليقات والوصف
            'يجب أن تكون هذه السطور الأولى في الملف بالضبط' => [
                'key' => 'file_header_comment',
                'ar' => 'يجب أن تكون هذه السطور الأولى في الملف بالضبط',
                'en' => 'These lines must be exactly the first in the file'
            ],
            'هذا الملف يحتوي على تعريف' => [
                'key' => 'file_contains_definition',
                'ar' => 'هذا الملف يحتوي على تعريف',
                'en' => 'This file contains definition of'
            ],
            'تنظيف أي نتائج متبقية' => [
                'key' => 'cleanup_remaining_results',
                'ar' => 'تنظيف أي نتائج متبقية',
                'en' => 'Clean up any remaining results'
            ],
            'عرض أي رسائل خطأ أو نجاح' => [
                'key' => 'display_error_success_messages',
                'ar' => 'عرض أي رسائل خطأ أو نجاح',
                'en' => 'Display any error or success messages'
            ],

            // النصوص من add_customer.php
            'تم إضافة العميل بنجاح' => [
                'key' => 'customer_added_successfully',
                'ar' => 'تم إضافة العميل بنجاح',
                'en' => 'Customer added successfully'
            ],
            'حدث خطأ أثناء إضافة العميل' => [
                'key' => 'error_adding_customer',
                'ar' => 'حدث خطأ أثناء إضافة العميل',
                'en' => 'Error occurred while adding customer'
            ],
            'إضافة عميل جديد' => [
                'key' => 'add_new_customer',
                'ar' => 'إضافة عميل جديد',
                'en' => 'Add New Customer'
            ],
            'اسم العميل' => [
                'key' => 'customer_name',
                'ar' => 'اسم العميل',
                'en' => 'Customer Name'
            ],
            'رقم الجوال' => [
                'key' => 'mobile_number',
                'ar' => 'رقم الجوال',
                'en' => 'Mobile Number'
            ],
            'البريد الإلكتروني' => [
                'key' => 'email_address',
                'ar' => 'البريد الإلكتروني',
                'en' => 'Email Address'
            ],
            'الرقم الضريبي' => [
                'key' => 'tax_number',
                'ar' => 'الرقم الضريبي',
                'en' => 'Tax Number'
            ],
            'العنوان' => [
                'key' => 'address',
                'ar' => 'العنوان',
                'en' => 'Address'
            ],
            'حفظ' => [
                'key' => 'save',
                'ar' => 'حفظ',
                'en' => 'Save'
            ],
            'إلغاء' => [
                'key' => 'cancel',
                'ar' => 'إلغاء',
                'en' => 'Cancel'
            ],

            // النصوص من purchases.php
            'جدول المشتريات غير موجود في قاعدة البيانات' => [
                'key' => 'purchases_table_not_found',
                'ar' => 'جدول المشتريات غير موجود في قاعدة البيانات',
                'en' => 'Purchases table not found in database'
            ],
            'إضافة معلومات تصحيح عن قاعدة البيانات' => [
                'key' => 'add_database_debug_info',
                'ar' => 'إضافة معلومات تصحيح عن قاعدة البيانات',
                'en' => 'Add database debug information'
            ],
            'التحقق من بنية جدول المشتريات' => [
                'key' => 'check_purchases_table_structure',
                'ar' => 'التحقق من بنية جدول المشتريات',
                'en' => 'Check purchases table structure'
            ],
            'إحصائيات المشتريات' => [
                'key' => 'purchases_statistics',
                'ar' => 'إحصائيات المشتريات',
                'en' => 'Purchases Statistics'
            ],
            'جدول المشتريات' => [
                'key' => 'purchases_table',
                'ar' => 'جدول المشتريات',
                'en' => 'Purchases Table'
            ],
            'القائمة الجانبية' => [
                'key' => 'sidebar',
                'ar' => 'القائمة الجانبية',
                'en' => 'Sidebar'
            ],
            'بناء الاستعلام مع الفلاتر' => [
                'key' => 'build_query_with_filters',
                'ar' => 'بناء الاستعلام مع الفلاتر',
                'en' => 'Build query with filters'
            ],
            'فلتر البحث' => [
                'key' => 'search_filter',
                'ar' => 'فلتر البحث',
                'en' => 'Search Filter'
            ],
            'فلتر العميل' => [
                'key' => 'customer_filter',
                'ar' => 'فلتر العميل',
                'en' => 'Customer Filter'
            ],
            'فلتر التاريخ من' => [
                'key' => 'date_from_filter',
                'ar' => 'فلتر التاريخ من',
                'en' => 'Date From Filter'
            ],
            'فلتر التاريخ إلى' => [
                'key' => 'date_to_filter',
                'ar' => 'فلتر التاريخ إلى',
                'en' => 'Date To Filter'
            ],
            'بناء جملة' => [
                'key' => 'build_clause',
                'ar' => 'بناء جملة',
                'en' => 'Build Clause'
            ],
            'التحقق من وجود جدول المشتريات مرة أخرى' => [
                'key' => 'recheck_purchases_table_existence',
                'ar' => 'التحقق من وجود جدول المشتريات مرة أخرى',
                'en' => 'Recheck purchases table existence'
            ],
            'التحقق من وجود الأعمدة المطلوبة في جدول المشتريات' => [
                'key' => 'check_required_columns_purchases_table',
                'ar' => 'التحقق من وجود الأعمدة المطلوبة في جدول المشتريات',
                'en' => 'Check required columns in purchases table'
            ],
            'بناء قائمة الأعمدة بناءً على ما هو متاح' => [
                'key' => 'build_columns_list_based_available',
                'ar' => 'بناء قائمة الأعمدة بناءً على ما هو متاح',
                'en' => 'Build columns list based on what is available'
            ],
            'تحديد الأعمدة المطلوبة بشكل صريح بدلاً من استخدام' => [
                'key' => 'specify_required_columns_explicitly',
                'ar' => 'تحديد الأعمدة المطلوبة بشكل صريح بدلاً من استخدام',
                'en' => 'Specify required columns explicitly instead of using'
            ],
            'استعلام بديل بدون عمود' => [
                'key' => 'alternative_query_without_column',
                'ar' => 'استعلام بديل بدون عمود',
                'en' => 'Alternative query without column'
            ],
            'فشل في إعداد الاستعلام' => [
                'key' => 'failed_to_prepare_query',
                'ar' => 'فشل في إعداد الاستعلام',
                'en' => 'Failed to prepare query'
            ],
            'إضافة معلومات تصحيح للمطور' => [
                'key' => 'add_debug_info_for_developer',
                'ar' => 'إضافة معلومات تصحيح للمطور',
                'en' => 'Add debug information for developer'
            ],
            'إضافة معلومات تصحيح للصف الحالي' => [
                'key' => 'add_debug_info_current_row',
                'ar' => 'إضافة معلومات تصحيح للصف الحالي',
                'en' => 'Add debug information for current row'
            ],
            'التحقق من وجود الحقول المطلوبة' => [
                'key' => 'check_required_fields_existence',
                'ar' => 'التحقق من وجود الحقول المطلوبة',
                'en' => 'Check required fields existence'
            ],
            'استخدام قيمة مباشرة إذا كانت موجودة' => [
                'key' => 'use_direct_value_if_exists',
                'ar' => 'استخدام قيمة مباشرة إذا كانت موجودة',
                'en' => 'Use direct value if exists'
            ],
            'حساب من' => [
                'key' => 'calculate_from',
                'ar' => 'حساب من',
                'en' => 'Calculate from'
            ],
            'قيمة افتراضية' => [
                'key' => 'default_value',
                'ar' => 'قيمة افتراضية',
                'en' => 'Default value'
            ],
            'عرض رسالة الخطأ للمطور' => [
                'key' => 'display_error_message_developer',
                'ar' => 'عرض رسالة الخطأ للمطور',
                'en' => 'Display error message for developer'
            ],
            'سيتم إخفاؤها في الإنتاج' => [
                'key' => 'will_be_hidden_in_production',
                'ar' => 'سيتم إخفاؤها في الإنتاج',
                'en' => 'Will be hidden in production'
            ],
            'تعريف وضع التصحيح محليًا' => [
                'key' => 'define_debug_mode_locally',
                'ar' => 'تعريف وضع التصحيح محليًا',
                'en' => 'Define debug mode locally'
            ],
            'يمكن نقل هذا إلى ملف التكوين لاحقًا' => [
                'key' => 'can_move_to_config_file_later',
                'ar' => 'يمكن نقل هذا إلى ملف التكوين لاحقًا',
                'en' => 'Can move this to config file later'
            ],
            'تغيير إلى في بيئة الإنتاج' => [
                'key' => 'change_to_in_production_environment',
                'ar' => 'تغيير إلى في بيئة الإنتاج',
                'en' => 'Change to in production environment'
            ],
            'عرض رسالة خطأ أكثر تفصيلاً للمستخدم في بيئة التطوير' => [
                'key' => 'display_detailed_error_message_development',
                'ar' => 'عرض رسالة خطأ أكثر تفصيلاً للمستخدم في بيئة التطوير',
                'en' => 'Display detailed error message for user in development environment'
            ],

            // النصوص من sales.php
            'إحصائيات المبيعات' => [
                'key' => 'sales_statistics',
                'ar' => 'إحصائيات المبيعات',
                'en' => 'Sales Statistics'
            ],
            'جدول المبيعات' => [
                'key' => 'sales_table',
                'ar' => 'جدول المبيعات',
                'en' => 'Sales Table'
            ],

            // نصوص إضافية شائعة
            'تم التحديث بنجاح' => [
                'key' => 'updated_successfully',
                'ar' => 'تم التحديث بنجاح',
                'en' => 'Updated successfully'
            ],
            'تم الحذف بنجاح' => [
                'key' => 'deleted_successfully',
                'ar' => 'تم الحذف بنجاح',
                'en' => 'Deleted successfully'
            ],
            'حدث خطأ أثناء التحديث' => [
                'key' => 'error_during_update',
                'ar' => 'حدث خطأ أثناء التحديث',
                'en' => 'Error occurred during update'
            ],
            'حدث خطأ أثناء الحذف' => [
                'key' => 'error_during_delete',
                'ar' => 'حدث خطأ أثناء الحذف',
                'en' => 'Error occurred during delete'
            ],
            'هل أنت متأكد من الحذف؟' => [
                'key' => 'confirm_delete_question',
                'ar' => 'هل أنت متأكد من الحذف؟',
                'en' => 'Are you sure you want to delete?'
            ],
            'لا توجد بيانات للعرض' => [
                'key' => 'no_data_to_display',
                'ar' => 'لا توجد بيانات للعرض',
                'en' => 'No data to display'
            ],
            'جاري التحميل' => [
                'key' => 'loading',
                'ar' => 'جاري التحميل',
                'en' => 'Loading'
            ],
            'يرجى الانتظار' => [
                'key' => 'please_wait',
                'ar' => 'يرجى الانتظار',
                'en' => 'Please wait'
            ],
            'تم بنجاح' => [
                'key' => 'completed_successfully',
                'ar' => 'تم بنجاح',
                'en' => 'Completed successfully'
            ],
            'فشل في العملية' => [
                'key' => 'operation_failed',
                'ar' => 'فشل في العملية',
                'en' => 'Operation failed'
            ],
            'البيانات مطلوبة' => [
                'key' => 'data_required',
                'ar' => 'البيانات مطلوبة',
                'en' => 'Data required'
            ],
            'تنسيق غير صحيح' => [
                'key' => 'invalid_format',
                'ar' => 'تنسيق غير صحيح',
                'en' => 'Invalid format'
            ],
            'القيمة غير صالحة' => [
                'key' => 'invalid_value',
                'ar' => 'القيمة غير صالحة',
                'en' => 'Invalid value'
            ],
            'الحقل مطلوب' => [
                'key' => 'field_required',
                'ar' => 'الحقل مطلوب',
                'en' => 'Field required'
            ],
            'تم الإرسال' => [
                'key' => 'submitted',
                'ar' => 'تم الإرسال',
                'en' => 'Submitted'
            ],
            'تم الاستلام' => [
                'key' => 'received',
                'ar' => 'تم الاستلام',
                'en' => 'Received'
            ],
            'في الانتظار' => [
                'key' => 'pending',
                'ar' => 'في الانتظار',
                'en' => 'Pending'
            ],
            'مكتمل' => [
                'key' => 'completed',
                'ar' => 'مكتمل',
                'en' => 'Completed'
            ],
            'ملغي' => [
                'key' => 'cancelled',
                'ar' => 'ملغي',
                'en' => 'Cancelled'
            ],
            'مؤجل' => [
                'key' => 'postponed',
                'ar' => 'مؤجل',
                'en' => 'Postponed'
            ],
            'قيد المراجعة' => [
                'key' => 'under_review',
                'ar' => 'قيد المراجعة',
                'en' => 'Under review'
            ],
            'تمت الموافقة' => [
                'key' => 'approved',
                'ar' => 'تمت الموافقة',
                'en' => 'Approved'
            ],
            'مرفوض' => [
                'key' => 'rejected',
                'ar' => 'مرفوض',
                'en' => 'Rejected'
            ]
        ];

        // تحميل ملفات الترجمة الحالية
        $ar_file = __DIR__ . '/languages/ar/lang.php';
        $en_file = __DIR__ . '/languages/en/lang.php';

        $ar_translations = file_exists($ar_file) ? require $ar_file : [];
        $en_translations = file_exists($en_file) ? require $en_file : [];

        // إضافة الترجمات المفقودة
        foreach ($comprehensive_arabic_texts as $arabic_text => $translation) {
            $key = $translation['key'];

            if (!isset($ar_translations[$key])) {
                $ar_translations[$key] = $translation['ar'];
                $translations_added++;
            }

            if (!isset($en_translations[$key])) {
                $en_translations[$key] = $translation['en'];
                $translations_added++;
            }
        }

        // حفظ ملفات الترجمة المحدثة
        $ar_content = "<?php\n\nreturn " . var_export($ar_translations, true) . ";\n";
        $en_content = "<?php\n\nreturn " . var_export($en_translations, true) . ";\n";

        if (file_put_contents($ar_file, $ar_content)) {
            $files_updated++;
        } else {
            $errors[] = "فشل في حفظ ملف الترجمة العربية";
        }

        if (file_put_contents($en_file, $en_content)) {
            $files_updated++;
        } else {
            $errors[] = "فشل في حفظ ملف الترجمة الإنجليزية";
        }

        if ($files_updated > 0) {
            $_SESSION['success'] = "تم تحديث $files_updated ملف ترجمة وإضافة $translations_added ترجمة شاملة";
        }

        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', $errors);
        }

    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء الترجمة الشاملة: " . $e->getMessage();
    }

    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-gradient-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-language"></i>
                        ترجمة شاملة لجميع النصوص العربية في المشروع
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> حول الأداة الشاملة</h6>
                        <p>هذه الأداة تقوم بفحص وترجمة جميع النصوص العربية المكتشفة في المشروع بشكل شامل ومتقدم.</p>
                        <ul>
                            <li><strong>100+ نص عربي</strong>: جميع النصوص المكتشفة في الملفات</li>
                            <li><strong>ترجمة تلقائية</strong>: إضافة الترجمات إلى ملفات اللغة</li>
                            <li><strong>تصنيف متقدم</strong>: تعليقات، رسائل، واجهة، حالات</li>
                            <li><strong>معالجة شاملة</strong>: جميع أنواع النصوص والسياقات</li>
                        </ul>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">النصوص المكتشفة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-comment text-primary"></i> <strong>تعليقات الكود</strong>: 15+ تعليق</li>
                                        <li><i class="fas fa-exclamation-circle text-warning"></i> <strong>رسائل النظام</strong>: 20+ رسالة</li>
                                        <li><i class="fas fa-desktop text-info"></i> <strong>عناصر الواجهة</strong>: 25+ عنصر</li>
                                        <li><i class="fas fa-database text-secondary"></i> <strong>رسائل قاعدة البيانات</strong>: 30+ رسالة</li>
                                        <li><i class="fas fa-cog text-dark"></i> <strong>حالات النظام</strong>: 15+ حالة</li>
                                        <li><i class="fas fa-bug text-danger"></i> <strong>رسائل التصحيح</strong>: 10+ رسالة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">الإحصائيات</h6>
                                </div>
                                <div class="card-body">
                                    <?php
                                    // إحصائيات سريعة
                                    $ar_file = __DIR__ . '/languages/ar/lang.php';
                                    $en_file = __DIR__ . '/languages/en/lang.php';

                                    $ar_count = 0;
                                    $en_count = 0;

                                    if (file_exists($ar_file)) {
                                        $ar_translations = require $ar_file;
                                        $ar_count = count($ar_translations);
                                    }

                                    if (file_exists($en_file)) {
                                        $en_translations = require $en_file;
                                        $en_count = count($en_translations);
                                    }

                                    $new_texts_count = count($comprehensive_arabic_texts ?? []);
                                    ?>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-flag text-success"></i> ترجمات عربية حالية: <strong><?php echo $ar_count; ?></strong></li>
                                        <li><i class="fas fa-flag text-primary"></i> ترجمات إنجليزية حالية: <strong><?php echo $en_count; ?></strong></li>
                                        <li><i class="fas fa-plus text-info"></i> نصوص جديدة مكتشفة: <strong><?php echo $new_texts_count; ?></strong></li>
                                        <li><i class="fas fa-file text-secondary"></i> ملفات محدثة: <strong>2</strong></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6>أمثلة على النصوص المترجمة:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>النص العربي</th>
                                            <th>المفتاح</th>
                                            <th>الترجمة الإنجليزية</th>
                                            <th>النوع</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>تم إضافة العميل بنجاح</td>
                                            <td><code>customer_added_successfully</code></td>
                                            <td>Customer added successfully</td>
                                            <td><span class="badge bg-success">رسالة نجاح</span></td>
                                        </tr>
                                        <tr>
                                            <td>إضافة عميل جديد</td>
                                            <td><code>add_new_customer</code></td>
                                            <td>Add New Customer</td>
                                            <td><span class="badge bg-primary">عنوان صفحة</span></td>
                                        </tr>
                                        <tr>
                                            <td>جدول المشتريات غير موجود في قاعدة البيانات</td>
                                            <td><code>purchases_table_not_found</code></td>
                                            <td>Purchases table not found in database</td>
                                            <td><span class="badge bg-danger">رسالة خطأ</span></td>
                                        </tr>
                                        <tr>
                                            <td>إحصائيات المبيعات</td>
                                            <td><code>sales_statistics</code></td>
                                            <td>Sales Statistics</td>
                                            <td><span class="badge bg-info">عنصر واجهة</span></td>
                                        </tr>
                                        <tr>
                                            <td>قيد المراجعة</td>
                                            <td><code>under_review</code></td>
                                            <td>Under review</td>
                                            <td><span class="badge bg-warning">حالة</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم</h6>
                        <p>هذه الأداة ستقوم بإضافة 100+ ترجمة جديدة إلى ملفات اللغة. يُنصح بشدة بعمل نسخة احتياطية قبل التطبيق.</p>
                        <ul class="mb-0">
                            <li>تأكد من وجود مجلد languages وملفات الترجمة</li>
                            <li>هذه الأداة تضيف الترجمات فقط ولا تستبدل النصوص في الكود</li>
                            <li>استخدم أدوات الاستبدال الأخرى لتطبيق الترجمات في الكود</li>
                        </ul>
                    </div>

                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من إضافة جميع الترجمات الشاملة؟ سيتم إضافة 100+ ترجمة جديدة.')">
                        <div class="text-center">
                            <button type="submit" name="comprehensive_translate" class="btn btn-gradient-primary btn-lg">
                                <i class="fas fa-language"></i> إضافة جميع الترجمات الشاملة
                            </button>
                        </div>
                    </form>

                    <div class="mt-4">
                        <h6>الخطوات التالية الموصى بها:</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <a href="scan_arabic_terms.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-search-plus"></i> فحص المصطلحات
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="replace_hardcoded_texts.php" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-exchange-alt"></i> استبدال النصوص
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="translate_arabic_terms.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-language"></i> ترجمة المصطلحات
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="review_translations.php" class="btn btn-secondary w-100 mb-2">
                                    <i class="fas fa-check"></i> مراجعة النتائج
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- نصائح متقدمة -->
                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-lightbulb"></i> نصائح للاستخدام الأمثل</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li><strong>ابدأ بهذه الأداة</strong>: لإضافة جميع الترجمات</li>
                                    <li><strong>ثم استخدم أدوات الاستبدال</strong>: لتطبيق الترجمات</li>
                                    <li><strong>اختبر النظام</strong>: بعد كل خطوة</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="mb-0">
                                    <li><strong>راجع الترجمات</strong>: للتأكد من الدقة</li>
                                    <li><strong>عدّل حسب الحاجة</strong>: في ملفات اللغة</li>
                                    <li><strong>وثّق التغييرات</strong>: للمراجعة المستقبلية</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>