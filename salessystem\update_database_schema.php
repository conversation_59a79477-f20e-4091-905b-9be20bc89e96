<?php
/**
 * أداة تحديث بنية قاعدة البيانات الشاملة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// بنية الجداول المحدثة والكاملة
$updated_schema = [
    'customers' => [
        'description' => 'جدول العملاء',
        'columns' => [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'name' => 'VARCHAR(100) NOT NULL',
            'phone' => 'VARCHAR(20) DEFAULT NULL',
            'email' => 'VARCHAR(100) DEFAULT NULL',
            'tax_number' => 'VARCHAR(50) DEFAULT NULL',
            'address' => 'TEXT DEFAULT NULL',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ],
        'indexes' => [
            'idx_customer_email' => 'INDEX (email)',
            'idx_customer_phone' => 'INDEX (phone)',
            'idx_customer_tax_number' => 'INDEX (tax_number)'
        ]
    ],
    'products' => [
        'description' => 'جدول المنتجات',
        'columns' => [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'name' => 'VARCHAR(100) NOT NULL',
            'description' => 'TEXT DEFAULT NULL',
            'price' => 'DECIMAL(10,2) NOT NULL',
            'tax_rate' => 'DECIMAL(5,2) DEFAULT 0',
            'stock_quantity' => 'INT DEFAULT 0',
            'unit' => 'VARCHAR(20) DEFAULT "قطعة"',
            'barcode' => 'VARCHAR(50) DEFAULT NULL',
            'category' => 'VARCHAR(50) DEFAULT NULL',
            'is_active' => 'BOOLEAN DEFAULT TRUE',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ],
        'indexes' => [
            'idx_product_barcode' => 'UNIQUE INDEX (barcode)',
            'idx_product_category' => 'INDEX (category)',
            'idx_product_active' => 'INDEX (is_active)'
        ]
    ],
    'sales' => [
        'description' => 'جدول المبيعات',
        'columns' => [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'customer_id' => 'INT DEFAULT NULL',
            'invoice_number' => 'VARCHAR(50) UNIQUE NOT NULL',
            'date' => 'DATE NOT NULL',
            'subtotal' => 'DECIMAL(10,2) NOT NULL',
            'tax_amount' => 'DECIMAL(10,2) NOT NULL',
            'total_amount' => 'DECIMAL(10,2) NOT NULL',
            'discount_amount' => 'DECIMAL(10,2) DEFAULT 0',
            'payment_method' => 'VARCHAR(50) DEFAULT "نقدي"',
            'payment_status' => 'ENUM("مدفوع", "غير مدفوع", "مدفوع جزئياً") DEFAULT "مدفوع"',
            'notes' => 'TEXT DEFAULT NULL',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ],
        'foreign_keys' => [
            'fk_sales_customer' => 'FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL'
        ],
        'indexes' => [
            'idx_sales_date' => 'INDEX (date)',
            'idx_sales_customer' => 'INDEX (customer_id)',
            'idx_sales_payment_status' => 'INDEX (payment_status)'
        ]
    ],
    'sale_items' => [
        'description' => 'عناصر المبيعات',
        'columns' => [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'sale_id' => 'INT NOT NULL',
            'product_id' => 'INT NOT NULL',
            'product_name' => 'VARCHAR(100) NOT NULL',
            'quantity' => 'INT NOT NULL',
            'unit_price' => 'DECIMAL(10,2) NOT NULL',
            'tax_rate' => 'DECIMAL(5,2) NOT NULL',
            'tax_amount' => 'DECIMAL(10,2) NOT NULL',
            'total_price' => 'DECIMAL(10,2) NOT NULL',
            'discount_amount' => 'DECIMAL(10,2) DEFAULT 0'
        ],
        'foreign_keys' => [
            'fk_sale_items_sale' => 'FOREIGN KEY (sale_id) REFERENCES sales(id) ON DELETE CASCADE',
            'fk_sale_items_product' => 'FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT'
        ]
    ],
    'purchases' => [
        'description' => 'جدول المشتريات',
        'columns' => [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'supplier_name' => 'VARCHAR(100) NOT NULL',
            'supplier_phone' => 'VARCHAR(20) DEFAULT NULL',
            'supplier_email' => 'VARCHAR(100) DEFAULT NULL',
            'supplier_tax_number' => 'VARCHAR(50) DEFAULT NULL',
            'invoice_number' => 'VARCHAR(50) UNIQUE NOT NULL',
            'date' => 'DATE NOT NULL',
            'subtotal' => 'DECIMAL(10,2) NOT NULL',
            'tax_amount' => 'DECIMAL(10,2) NOT NULL',
            'total_amount' => 'DECIMAL(10,2) NOT NULL',
            'discount_amount' => 'DECIMAL(10,2) DEFAULT 0',
            'payment_method' => 'VARCHAR(50) DEFAULT "نقدي"',
            'payment_status' => 'ENUM("مدفوع", "غير مدفوع", "مدفوع جزئياً") DEFAULT "مدفوع"',
            'notes' => 'TEXT DEFAULT NULL',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ],
        'indexes' => [
            'idx_purchases_date' => 'INDEX (date)',
            'idx_purchases_supplier' => 'INDEX (supplier_name)',
            'idx_purchases_payment_status' => 'INDEX (payment_status)'
        ]
    ],
    'purchase_items' => [
        'description' => 'عناصر المشتريات',
        'columns' => [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'purchase_id' => 'INT NOT NULL',
            'product_id' => 'INT NOT NULL',
            'product_name' => 'VARCHAR(100) NOT NULL',
            'quantity' => 'INT NOT NULL',
            'unit_price' => 'DECIMAL(10,2) NOT NULL',
            'tax_rate' => 'DECIMAL(5,2) NOT NULL',
            'tax_amount' => 'DECIMAL(10,2) NOT NULL',
            'total_price' => 'DECIMAL(10,2) NOT NULL',
            'discount_amount' => 'DECIMAL(10,2) DEFAULT 0'
        ],
        'foreign_keys' => [
            'fk_purchase_items_purchase' => 'FOREIGN KEY (purchase_id) REFERENCES purchases(id) ON DELETE CASCADE',
            'fk_purchase_items_product' => 'FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT'
        ]
    ],
    'settings' => [
        'description' => 'إعدادات النظام',
        'columns' => [
            'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
            'setting_key' => 'VARCHAR(100) UNIQUE NOT NULL',
            'setting_value' => 'TEXT DEFAULT NULL',
            'setting_type' => 'ENUM("text", "number", "boolean", "email", "url", "textarea") DEFAULT "text"',
            'category' => 'VARCHAR(50) DEFAULT "general"',
            'description' => 'TEXT DEFAULT NULL',
            'is_required' => 'BOOLEAN DEFAULT FALSE',
            'default_value' => 'TEXT DEFAULT NULL',
            'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
        ],
        'indexes' => [
            'idx_settings_category' => 'INDEX (category)',
            'idx_settings_type' => 'INDEX (setting_type)'
        ]
    ]
];

// معالجة طلب التحديث
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_schema'])) {
    $updated_tables = 0;
    $added_columns = 0;
    $added_indexes = 0;
    $errors = [];
    
    try {
        foreach ($updated_schema as $table_name => $table_info) {
            // فحص وجود الجدول
            $table_exists = $db->query("SHOW TABLES LIKE '$table_name'");
            
            if (!$table_exists || $table_exists->num_rows == 0) {
                // إنشاء الجدول
                $create_sql = "CREATE TABLE `$table_name` (\n";
                $column_definitions = [];
                
                foreach ($table_info['columns'] as $column_name => $column_definition) {
                    $column_definitions[] = "    `$column_name` $column_definition";
                }
                
                $create_sql .= implode(",\n", $column_definitions);
                
                // إضافة المفاتيح الخارجية
                if (isset($table_info['foreign_keys'])) {
                    foreach ($table_info['foreign_keys'] as $fk_name => $fk_definition) {
                        $create_sql .= ",\n    $fk_definition";
                    }
                }
                
                // إضافة الفهارس
                if (isset($table_info['indexes'])) {
                    foreach ($table_info['indexes'] as $index_name => $index_definition) {
                        $create_sql .= ",\n    $index_definition";
                    }
                }
                
                $create_sql .= "\n) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                
                if ($db->query($create_sql)) {
                    $updated_tables++;
                } else {
                    $errors[] = "فشل في إنشاء جدول $table_name: " . $db->error;
                }
            } else {
                // فحص وتحديث بنية الجدول الموجود
                $existing_columns = [];
                $columns_result = $db->query("DESCRIBE `$table_name`");
                while ($row = $columns_result->fetch_assoc()) {
                    $existing_columns[$row['Field']] = $row;
                }
                
                // إضافة الأعمدة المفقودة
                foreach ($table_info['columns'] as $column_name => $column_definition) {
                    if (!isset($existing_columns[$column_name])) {
                        $alter_sql = "ALTER TABLE `$table_name` ADD COLUMN `$column_name` $column_definition";
                        if ($db->query($alter_sql)) {
                            $added_columns++;
                        } else {
                            $errors[] = "فشل في إضافة عمود $column_name إلى جدول $table_name: " . $db->error;
                        }
                    }
                }
                
                // إضافة الفهارس المفقودة
                if (isset($table_info['indexes'])) {
                    $existing_indexes = [];
                    $indexes_result = $db->query("SHOW INDEX FROM `$table_name`");
                    while ($row = $indexes_result->fetch_assoc()) {
                        $existing_indexes[$row['Key_name']] = $row;
                    }
                    
                    foreach ($table_info['indexes'] as $index_name => $index_definition) {
                        if (!isset($existing_indexes[$index_name]) && $index_name !== 'PRIMARY') {
                            $alter_sql = "ALTER TABLE `$table_name` ADD $index_definition";
                            if ($db->query($alter_sql)) {
                                $added_indexes++;
                            } else {
                                $errors[] = "فشل في إضافة فهرس $index_name إلى جدول $table_name: " . $db->error;
                            }
                        }
                    }
                }
            }
        }
        
        if ($updated_tables > 0 || $added_columns > 0 || $added_indexes > 0) {
            $success_msg = "تم التحديث بنجاح: ";
            if ($updated_tables > 0) $success_msg .= "$updated_tables جدول جديد، ";
            if ($added_columns > 0) $success_msg .= "$added_columns عمود جديد، ";
            if ($added_indexes > 0) $success_msg .= "$added_indexes فهرس جديد";
            $_SESSION['success'] = rtrim($success_msg, '، ');
        } else {
            $_SESSION['info'] = "بنية قاعدة البيانات محدثة بالفعل";
        }
        
        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', $errors);
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء تحديث قاعدة البيانات: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// فحص الوضع الحالي لقاعدة البيانات
$current_status = [];
$total_missing_columns = 0;
$total_missing_indexes = 0;
$total_missing_tables = 0;

foreach ($updated_schema as $table_name => $table_info) {
    $current_status[$table_name] = [
        'exists' => false,
        'missing_columns' => [],
        'missing_indexes' => [],
        'existing_columns' => [],
        'existing_indexes' => []
    ];
    
    // فحص وجود الجدول
    $table_exists = $db->query("SHOW TABLES LIKE '$table_name'");
    
    if (!$table_exists || $table_exists->num_rows == 0) {
        $current_status[$table_name]['exists'] = false;
        $current_status[$table_name]['missing_columns'] = array_keys($table_info['columns']);
        if (isset($table_info['indexes'])) {
            $current_status[$table_name]['missing_indexes'] = array_keys($table_info['indexes']);
        }
        $total_missing_tables++;
        $total_missing_columns += count($table_info['columns']);
        if (isset($table_info['indexes'])) {
            $total_missing_indexes += count($table_info['indexes']);
        }
    } else {
        $current_status[$table_name]['exists'] = true;
        
        // فحص الأعمدة
        $existing_columns = [];
        $columns_result = $db->query("DESCRIBE `$table_name`");
        while ($row = $columns_result->fetch_assoc()) {
            $existing_columns[$row['Field']] = $row;
            $current_status[$table_name]['existing_columns'][] = $row['Field'];
        }
        
        foreach ($table_info['columns'] as $column_name => $column_definition) {
            if (!isset($existing_columns[$column_name])) {
                $current_status[$table_name]['missing_columns'][] = $column_name;
                $total_missing_columns++;
            }
        }
        
        // فحص الفهارس
        if (isset($table_info['indexes'])) {
            $existing_indexes = [];
            $indexes_result = $db->query("SHOW INDEX FROM `$table_name`");
            while ($row = $indexes_result->fetch_assoc()) {
                $existing_indexes[$row['Key_name']] = $row;
                if (!in_array($row['Key_name'], $current_status[$table_name]['existing_indexes'])) {
                    $current_status[$table_name]['existing_indexes'][] = $row['Key_name'];
                }
            }
            
            foreach ($table_info['indexes'] as $index_name => $index_definition) {
                if (!isset($existing_indexes[$index_name])) {
                    $current_status[$table_name]['missing_indexes'][] = $index_name;
                    $total_missing_indexes++;
                }
            }
        }
    }
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-database"></i>
                        تحديث بنية قاعدة البيانات الشاملة
                    </h4>
                </div>
                <div class="card-body">
                    <!-- إحصائيات عامة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($updated_schema); ?></h3>
                                    <p class="mb-0">إجمالي الجداول</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $total_missing_tables; ?></h3>
                                    <p class="mb-0">جداول مفقودة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h3><?php echo $total_missing_columns; ?></h3>
                                    <p class="mb-0">أعمدة مفقودة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $total_missing_indexes; ?></h3>
                                    <p class="mb-0">فهارس مفقودة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التبويبات -->
                    <ul class="nav nav-tabs" id="schemaTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                <i class="fas fa-chart-pie"></i> نظرة عامة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="tables-tab" data-bs-toggle="tab" data-bs-target="#tables" type="button" role="tab">
                                <i class="fas fa-table"></i> تحليل الجداول
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="schema-tab" data-bs-toggle="tab" data-bs-target="#schema" type="button" role="tab">
                                <i class="fas fa-code"></i> البنية المطلوبة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="update-tab" data-bs-toggle="tab" data-bs-target="#update" type="button" role="tab">
                                <i class="fas fa-sync"></i> تحديث قاعدة البيانات
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-4" id="schemaTabContent">
                        <!-- تبويب النظرة العامة -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-pie text-primary"></i>
                                تحليل حالة قاعدة البيانات
                            </h5>

                            <?php
                            $total_tables = count($updated_schema);
                            $existing_tables = $total_tables - $total_missing_tables;
                            $completion_rate = $total_tables > 0 ? round(($existing_tables / $total_tables) * 100, 1) : 0;
                            ?>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">معدل اكتمال الجداول</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="progress mb-3" style="height: 25px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $completion_rate; ?>%">
                                                    <?php echo $completion_rate; ?>%
                                                </div>
                                            </div>
                                            <p><strong>موجودة:</strong> <?php echo $existing_tables; ?> من <?php echo $total_tables; ?></p>
                                            <p><strong>مفقودة:</strong> <?php echo $total_missing_tables; ?> جدول</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">إحصائيات التحديثات المطلوبة</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>جداول جديدة:</span>
                                                <span class="badge bg-danger"><?php echo $total_missing_tables; ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>أعمدة جديدة:</span>
                                                <span class="badge bg-warning"><?php echo $total_missing_columns; ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>فهارس جديدة:</span>
                                                <span class="badge bg-secondary"><?php echo $total_missing_indexes; ?></span>
                                            </div>
                                            <hr>
                                            <div class="d-flex justify-content-between">
                                                <strong>إجمالي التحديثات:</strong>
                                                <span class="badge bg-primary"><?php echo ($total_missing_tables + $total_missing_columns + $total_missing_indexes); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <?php if ($total_missing_tables > 0 || $total_missing_columns > 0 || $total_missing_indexes > 0): ?>
                            <div class="alert alert-warning mt-4">
                                <h6><i class="fas fa-exclamation-triangle"></i> تحديثات مطلوبة</h6>
                                <p>تم العثور على تحديثات مطلوبة لقاعدة البيانات. يُنصح بتطبيقها لضمان عمل النظام بشكل صحيح.</p>
                                <ul>
                                    <?php if ($total_missing_tables > 0): ?>
                                    <li>إنشاء <?php echo $total_missing_tables; ?> جدول جديد</li>
                                    <?php endif; ?>
                                    <?php if ($total_missing_columns > 0): ?>
                                    <li>إضافة <?php echo $total_missing_columns; ?> عمود جديد</li>
                                    <?php endif; ?>
                                    <?php if ($total_missing_indexes > 0): ?>
                                    <li>إضافة <?php echo $total_missing_indexes; ?> فهرس جديد</li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-success mt-4">
                                <h6><i class="fas fa-check-circle"></i> ممتاز!</h6>
                                <p>بنية قاعدة البيانات محدثة ومكتملة. النظام جاهز للاستخدام.</p>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب تحليل الجداول -->
                        <div class="tab-pane fade" id="tables" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-table text-info"></i>
                                تحليل مفصل للجداول
                            </h5>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>الجدول</th>
                                            <th>الوصف</th>
                                            <th>الحالة</th>
                                            <th>الأعمدة المفقودة</th>
                                            <th>الفهارس المفقودة</th>
                                            <th>التفاصيل</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($updated_schema as $table_name => $table_info): ?>
                                        <tr>
                                            <td><code><?php echo $table_name; ?></code></td>
                                            <td><?php echo $table_info['description']; ?></td>
                                            <td>
                                                <?php if ($current_status[$table_name]['exists']): ?>
                                                    <span class="badge bg-success">موجود</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">مفقود</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($current_status[$table_name]['missing_columns'])): ?>
                                                    <span class="badge bg-warning"><?php echo count($current_status[$table_name]['missing_columns']); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-success">0</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($current_status[$table_name]['missing_indexes'])): ?>
                                                    <span class="badge bg-secondary"><?php echo count($current_status[$table_name]['missing_indexes']); ?></span>
                                                <?php else: ?>
                                                    <span class="badge bg-success">0</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#details-<?php echo $table_name; ?>">
                                                    عرض التفاصيل
                                                </button>
                                            </td>
                                        </tr>
                                        <tr class="collapse" id="details-<?php echo $table_name; ?>">
                                            <td colspan="6">
                                                <div class="card card-body">
                                                    <div class="row">
                                                        <?php if (!empty($current_status[$table_name]['missing_columns'])): ?>
                                                        <div class="col-md-6">
                                                            <h6 class="text-warning">الأعمدة المفقودة:</h6>
                                                            <ul class="list-unstyled">
                                                                <?php foreach ($current_status[$table_name]['missing_columns'] as $column): ?>
                                                                <li><code><?php echo $column; ?></code></li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($current_status[$table_name]['missing_indexes'])): ?>
                                                        <div class="col-md-6">
                                                            <h6 class="text-secondary">الفهارس المفقودة:</h6>
                                                            <ul class="list-unstyled">
                                                                <?php foreach ($current_status[$table_name]['missing_indexes'] as $index): ?>
                                                                <li><code><?php echo $index; ?></code></li>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        </div>
                                                        <?php endif; ?>

                                                        <?php if (!empty($current_status[$table_name]['existing_columns'])): ?>
                                                        <div class="col-md-12 mt-3">
                                                            <h6 class="text-success">الأعمدة الموجودة:</h6>
                                                            <div class="d-flex flex-wrap">
                                                                <?php foreach ($current_status[$table_name]['existing_columns'] as $column): ?>
                                                                <span class="badge bg-success me-1 mb-1"><?php echo $column; ?></span>
                                                                <?php endforeach; ?>
                                                            </div>
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- تبويب البنية المطلوبة -->
                        <div class="tab-pane fade" id="schema" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-code text-secondary"></i>
                                البنية المطلوبة لقاعدة البيانات
                            </h5>

                            <div class="accordion" id="schemaAccordion">
                                <?php foreach ($updated_schema as $table_name => $table_info): ?>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="heading-<?php echo $table_name; ?>">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-<?php echo $table_name; ?>">
                                            <strong><?php echo $table_name; ?></strong> - <?php echo $table_info['description']; ?>
                                        </button>
                                    </h2>
                                    <div id="collapse-<?php echo $table_name; ?>" class="accordion-collapse collapse" data-bs-parent="#schemaAccordion">
                                        <div class="accordion-body">
                                            <h6>الأعمدة:</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>اسم العمود</th>
                                                            <th>نوع البيانات</th>
                                                            <th>الحالة</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($table_info['columns'] as $column_name => $column_definition): ?>
                                                        <tr>
                                                            <td><code><?php echo $column_name; ?></code></td>
                                                            <td><small><?php echo $column_definition; ?></small></td>
                                                            <td>
                                                                <?php if (in_array($column_name, $current_status[$table_name]['missing_columns'])): ?>
                                                                    <span class="badge bg-danger">مفقود</span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-success">موجود</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>

                                            <?php if (isset($table_info['indexes'])): ?>
                                            <h6 class="mt-3">الفهارس:</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>اسم الفهرس</th>
                                                            <th>تعريف الفهرس</th>
                                                            <th>الحالة</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($table_info['indexes'] as $index_name => $index_definition): ?>
                                                        <tr>
                                                            <td><code><?php echo $index_name; ?></code></td>
                                                            <td><small><?php echo $index_definition; ?></small></td>
                                                            <td>
                                                                <?php if (in_array($index_name, $current_status[$table_name]['missing_indexes'])): ?>
                                                                    <span class="badge bg-danger">مفقود</span>
                                                                <?php else: ?>
                                                                    <span class="badge bg-success">موجود</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                            <?php endif; ?>

                                            <?php if (isset($table_info['foreign_keys'])): ?>
                                            <h6 class="mt-3">المفاتيح الخارجية:</h6>
                                            <ul class="list-unstyled">
                                                <?php foreach ($table_info['foreign_keys'] as $fk_name => $fk_definition): ?>
                                                <li><code><?php echo $fk_definition; ?></code></li>
                                                <?php endforeach; ?>
                                            </ul>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- تبويب تحديث قاعدة البيانات -->
                        <div class="tab-pane fade" id="update" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-sync text-primary"></i>
                                تحديث قاعدة البيانات
                            </h5>

                            <?php if ($total_missing_tables > 0 || $total_missing_columns > 0 || $total_missing_indexes > 0): ?>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم</h6>
                                <p>سيتم تطبيق التحديثات التالية على قاعدة البيانات:</p>
                                <ul>
                                    <?php if ($total_missing_tables > 0): ?>
                                    <li><strong>إنشاء <?php echo $total_missing_tables; ?> جدول جديد</strong></li>
                                    <?php endif; ?>
                                    <?php if ($total_missing_columns > 0): ?>
                                    <li><strong>إضافة <?php echo $total_missing_columns; ?> عمود جديد</strong></li>
                                    <?php endif; ?>
                                    <?php if ($total_missing_indexes > 0): ?>
                                    <li><strong>إضافة <?php echo $total_missing_indexes; ?> فهرس جديد</strong></li>
                                    <?php endif; ?>
                                </ul>
                                <p><strong>ملاحظة:</strong> هذه العملية آمنة ولن تؤثر على البيانات الموجودة.</p>
                            </div>

                            <form method="POST" onsubmit="return confirm('هل أنت متأكد من تحديث بنية قاعدة البيانات؟ هذه العملية آمنة ولن تؤثر على البيانات الموجودة.')">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">تفاصيل التحديث</h6>
                                    </div>
                                    <div class="card-body">
                                        <?php foreach ($updated_schema as $table_name => $table_info): ?>
                                            <?php if (!$current_status[$table_name]['exists'] || !empty($current_status[$table_name]['missing_columns']) || !empty($current_status[$table_name]['missing_indexes'])): ?>
                                            <div class="mb-3">
                                                <h6 class="text-primary"><?php echo $table_name; ?> - <?php echo $table_info['description']; ?></h6>

                                                <?php if (!$current_status[$table_name]['exists']): ?>
                                                <div class="alert alert-info">
                                                    <i class="fas fa-plus"></i> سيتم إنشاء الجدول بالكامل مع جميع الأعمدة والفهارس
                                                </div>
                                                <?php else: ?>
                                                    <?php if (!empty($current_status[$table_name]['missing_columns'])): ?>
                                                    <div class="mb-2">
                                                        <small class="text-warning"><strong>أعمدة جديدة:</strong></small>
                                                        <div class="d-flex flex-wrap">
                                                            <?php foreach ($current_status[$table_name]['missing_columns'] as $column): ?>
                                                            <span class="badge bg-warning me-1 mb-1"><?php echo $column; ?></span>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>

                                                    <?php if (!empty($current_status[$table_name]['missing_indexes'])): ?>
                                                    <div class="mb-2">
                                                        <small class="text-secondary"><strong>فهارس جديدة:</strong></small>
                                                        <div class="d-flex flex-wrap">
                                                            <?php foreach ($current_status[$table_name]['missing_indexes'] as $index): ?>
                                                            <span class="badge bg-secondary me-1 mb-1"><?php echo $index; ?></span>
                                                            <?php endforeach; ?>
                                                        </div>
                                                    </div>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                            <hr>
                                            <?php endif; ?>
                                        <?php endforeach; ?>

                                        <div class="text-center">
                                            <button type="submit" name="update_schema" class="btn btn-primary btn-lg">
                                                <i class="fas fa-sync"></i> تطبيق جميع التحديثات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <?php else: ?>
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle"></i> قاعدة البيانات محدثة</h6>
                                <p>بنية قاعدة البيانات محدثة ومكتملة. لا توجد تحديثات مطلوبة.</p>
                            </div>

                            <div class="text-center">
                                <a href="system_tools.php" class="btn btn-secondary">
                                    <i class="fas fa-tools"></i> العودة إلى أدوات النظام
                                </a>
                                <a href="settings.php" class="btn btn-primary">
                                    <i class="fas fa-cog"></i> الإعدادات
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
