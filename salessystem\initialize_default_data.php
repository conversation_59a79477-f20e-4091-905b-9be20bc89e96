<?php
/**
 * أداة إضافة البيانات الافتراضية لقاعدة البيانات الجديدة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// البيانات الافتراضية للإعدادات
$default_settings = [
    // إعدادات الشركة
    ['company_name', 'اسم الشركة', 'text', 'company', 'اسم الشركة أو المؤسسة', true, 'شركتي'],
    ['company_address', 'عنوان الشركة', 'textarea', 'company', 'العنوان الكامل للشركة', true, 'المملكة العربية السعودية'],
    ['company_phone', '0501234567', 'text', 'company', 'رقم هاتف الشركة', true, '0501234567'],
    ['company_email', '<EMAIL>', 'email', 'company', 'البريد الإلكتروني للشركة', false, '<EMAIL>'],
    ['company_website', 'https://www.company.com', 'url', 'company', 'موقع الشركة الإلكتروني', false, 'https://www.company.com'],
    ['company_tax_number', '*********', 'text', 'company', 'الرقم الضريبي للشركة', false, '*********'],
    
    // الإعدادات العامة
    ['default_language', 'ar', 'text', 'general', 'اللغة الافتراضية للنظام', true, 'ar'],
    ['default_currency', 'ريال سعودي', 'text', 'general', 'العملة الافتراضية', true, 'ريال سعودي'],
    ['currency_symbol', 'ر.س', 'text', 'general', 'رمز العملة', true, 'ر.س'],
    ['currency_code', 'SAR', 'text', 'general', 'كود العملة', true, 'SAR'],
    ['decimal_places', '2', 'number', 'general', 'عدد الخانات العشرية', true, '2'],
    ['date_format', 'Y-m-d', 'text', 'general', 'تنسيق التاريخ', true, 'Y-m-d'],
    ['time_format', 'H:i:s', 'text', 'general', 'تنسيق الوقت', true, 'H:i:s'],
    ['timezone', 'Asia/Riyadh', 'text', 'general', 'المنطقة الزمنية', true, 'Asia/Riyadh'],
    
    // إعدادات الضريبة
    ['tax_enabled', 'true', 'boolean', 'tax', 'تفعيل نظام الضريبة', true, 'true'],
    ['default_tax_rate', '15', 'number', 'tax', 'نسبة الضريبة الافتراضية', true, '15'],
    ['tax_number_required', 'false', 'boolean', 'tax', 'إجبارية الرقم الضريبي', false, 'false'],
    ['tax_inclusive_pricing', 'false', 'boolean', 'tax', 'الأسعار شاملة الضريبة', false, 'false'],
    
    // إعدادات الفواتير
    ['invoice_prefix', 'INV-', 'text', 'invoice', 'بادئة رقم الفاتورة', true, 'INV-'],
    ['invoice_number_length', '6', 'number', 'invoice', 'طول رقم الفاتورة', true, '6'],
    ['invoice_footer', 'شكراً لتعاملكم معنا', 'textarea', 'invoice', 'تذييل الفاتورة', false, 'شكراً لتعاملكم معنا'],
    ['auto_invoice_number', 'true', 'boolean', 'invoice', 'ترقيم الفواتير تلقائياً', true, 'true'],
    
    // إعدادات النظام
    ['items_per_page', '20', 'number', 'system', 'عدد العناصر في الصفحة', true, '20'],
    ['backup_enabled', 'true', 'boolean', 'system', 'تفعيل النسخ الاحتياطي', false, 'true'],
    ['maintenance_mode', 'false', 'boolean', 'system', 'وضع الصيانة', false, 'false'],
    ['debug_mode', 'false', 'boolean', 'system', 'وضع التطوير', false, 'false'],
    
    // إعدادات الأمان
    ['session_timeout', '3600', 'number', 'security', 'مهلة انتهاء الجلسة (بالثواني)', true, '3600'],
    ['password_min_length', '6', 'number', 'security', 'الحد الأدنى لطول كلمة المرور', true, '6'],
    ['login_attempts', '5', 'number', 'security', 'عدد محاولات تسجيل الدخول المسموحة', true, '5'],
    ['account_lockout_time', '900', 'number', 'security', 'مدة قفل الحساب (بالثواني)', true, '900']
];

// معالجة طلب إضافة البيانات الافتراضية
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['initialize_data'])) {
    $added_settings = 0;
    $errors = [];
    
    try {
        // فحص وجود جدول الإعدادات
        $table_exists = $db->query("SHOW TABLES LIKE 'settings'");
        if (!$table_exists || $table_exists->num_rows == 0) {
            $errors[] = "جدول الإعدادات غير موجود. يرجى إنشاؤه أولاً.";
        } else {
            // إضافة الإعدادات الافتراضية
            $stmt = $db->prepare("INSERT IGNORE INTO settings (setting_key, setting_value, setting_type, category, description, is_required, default_value) VALUES (?, ?, ?, ?, ?, ?, ?)");
            
            foreach ($default_settings as $setting) {
                $stmt->bind_param("sssssss", $setting[0], $setting[1], $setting[2], $setting[3], $setting[4], $setting[5], $setting[6]);
                if ($stmt->execute()) {
                    if ($stmt->affected_rows > 0) {
                        $added_settings++;
                    }
                } else {
                    $errors[] = "فشل في إضافة إعداد {$setting[0]}: " . $stmt->error;
                }
            }
            $stmt->close();
        }
        
        if ($added_settings > 0) {
            $_SESSION['success'] = "تم إضافة $added_settings إعداد افتراضي بنجاح";
        } else {
            $_SESSION['info'] = "جميع الإعدادات الافتراضية موجودة بالفعل";
        }
        
        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', $errors);
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء إضافة البيانات الافتراضية: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// فحص الوضع الحالي
$current_settings = [];
$missing_settings = [];

// فحص وجود جدول الإعدادات
$table_exists = $db->query("SHOW TABLES LIKE 'settings'");
$settings_table_exists = ($table_exists && $table_exists->num_rows > 0);

if ($settings_table_exists) {
    // جلب الإعدادات الموجودة
    $result = $db->query("SELECT setting_key FROM settings");
    while ($row = $result->fetch_assoc()) {
        $current_settings[] = $row['setting_key'];
    }
    
    // تحديد الإعدادات المفقودة
    foreach ($default_settings as $setting) {
        if (!in_array($setting[0], $current_settings)) {
            $missing_settings[] = $setting;
        }
    }
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-seedling"></i>
                        إضافة البيانات الافتراضية
                    </h4>
                </div>
                <div class="card-body">
                    <!-- إحصائيات عامة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($default_settings); ?></h3>
                                    <p class="mb-0">إعدادات افتراضية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($current_settings); ?></h3>
                                    <p class="mb-0">إعدادات موجودة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h3><?php echo count($missing_settings); ?></h3>
                                    <p class="mb-0">إعدادات مفقودة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $settings_table_exists ? 'موجود' : 'مفقود'; ?></h3>
                                    <p class="mb-0">جدول الإعدادات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <?php if (!$settings_table_exists): ?>
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle"></i> جدول الإعدادات مفقود</h6>
                        <p>يجب إنشاء جدول الإعدادات أولاً قبل إضافة البيانات الافتراضية.</p>
                        <a href="create_settings_table.php" class="btn btn-danger" target="_blank">
                            <i class="fas fa-plus"></i> إنشاء جدول الإعدادات
                        </a>
                    </div>
                    <?php elseif (empty($missing_settings)): ?>
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle"></i> البيانات مكتملة</h6>
                        <p>جميع الإعدادات الافتراضية موجودة بالفعل. النظام جاهز للاستخدام.</p>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> إعدادات مفقودة</h6>
                        <p>يوجد <?php echo count($missing_settings); ?> إعداد افتراضي مفقود. يُنصح بإضافتها لضمان عمل النظام بشكل صحيح.</p>
                    </div>
                    <?php endif; ?>

                    <!-- التبويبات -->
                    <ul class="nav nav-tabs" id="dataTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                <i class="fas fa-chart-pie"></i> نظرة عامة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="missing-tab" data-bs-toggle="tab" data-bs-target="#missing" type="button" role="tab">
                                <i class="fas fa-exclamation-triangle"></i> الإعدادات المفقودة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                                <i class="fas fa-list"></i> جميع الإعدادات الافتراضية
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="actions-tab" data-bs-toggle="tab" data-bs-target="#actions" type="button" role="tab">
                                <i class="fas fa-play"></i> إجراءات التهيئة
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-4" id="dataTabContent">
                        <!-- تبويب النظرة العامة -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-pie text-primary"></i>
                                تحليل البيانات الافتراضية
                            </h5>
                            
                            <div class="row">
                                <?php
                                $categories = [];
                                foreach ($default_settings as $setting) {
                                    $category = $setting[3];
                                    if (!isset($categories[$category])) {
                                        $categories[$category] = ['total' => 0, 'missing' => 0];
                                    }
                                    $categories[$category]['total']++;
                                    
                                    if (!in_array($setting[0], $current_settings)) {
                                        $categories[$category]['missing']++;
                                    }
                                }
                                
                                $category_names = [
                                    'company' => 'إعدادات الشركة',
                                    'general' => 'الإعدادات العامة',
                                    'tax' => 'إعدادات الضريبة',
                                    'invoice' => 'إعدادات الفواتير',
                                    'system' => 'إعدادات النظام',
                                    'security' => 'إعدادات الأمان'
                                ];
                                ?>
                                
                                <?php foreach ($categories as $category => $data): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0"><?php echo $category_names[$category] ?? $category; ?></h6>
                                        </div>
                                        <div class="card-body">
                                            <?php
                                            $completion_rate = $data['total'] > 0 ? round((($data['total'] - $data['missing']) / $data['total']) * 100, 1) : 0;
                                            ?>
                                            <div class="progress mb-2" style="height: 20px;">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $completion_rate; ?>%">
                                                    <?php echo $completion_rate; ?>%
                                                </div>
                                            </div>
                                            <div class="d-flex justify-content-between">
                                                <small>موجودة: <?php echo ($data['total'] - $data['missing']); ?></small>
                                                <small>مفقودة: <?php echo $data['missing']; ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- تبويب الإعدادات المفقودة -->
                        <div class="tab-pane fade" id="missing" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                                الإعدادات المفقودة
                            </h5>

                            <?php if (empty($missing_settings)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                جميع الإعدادات الافتراضية موجودة!
                            </div>
                            <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المفتاح</th>
                                            <th>القيمة الافتراضية</th>
                                            <th>النوع</th>
                                            <th>الفئة</th>
                                            <th>الوصف</th>
                                            <th>مطلوب</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($missing_settings as $setting): ?>
                                        <tr>
                                            <td><code><?php echo htmlspecialchars($setting[0]); ?></code></td>
                                            <td><?php echo htmlspecialchars($setting[1]); ?></td>
                                            <td><span class="badge bg-info"><?php echo $setting[2]; ?></span></td>
                                            <td><span class="badge bg-secondary"><?php echo $setting[3]; ?></span></td>
                                            <td><?php echo htmlspecialchars($setting[4]); ?></td>
                                            <td>
                                                <?php if ($setting[5]): ?>
                                                    <span class="badge bg-danger">مطلوب</span>
                                                <?php else: ?>
                                                    <span class="badge bg-success">اختياري</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب جميع الإعدادات الافتراضية -->
                        <div class="tab-pane fade" id="all" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-list text-info"></i>
                                جميع الإعدادات الافتراضية
                            </h5>

                            <div class="table-responsive">
                                <table class="table table-striped table-sm">
                                    <thead>
                                        <tr>
                                            <th>المفتاح</th>
                                            <th>القيمة الافتراضية</th>
                                            <th>النوع</th>
                                            <th>الفئة</th>
                                            <th>الوصف</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($default_settings as $setting): ?>
                                        <tr>
                                            <td><code><?php echo htmlspecialchars($setting[0]); ?></code></td>
                                            <td><?php echo htmlspecialchars($setting[1]); ?></td>
                                            <td><span class="badge bg-info"><?php echo $setting[2]; ?></span></td>
                                            <td><span class="badge bg-secondary"><?php echo $setting[3]; ?></span></td>
                                            <td><?php echo htmlspecialchars($setting[4]); ?></td>
                                            <td>
                                                <?php if (in_array($setting[0], $current_settings)): ?>
                                                    <span class="badge bg-success">موجود</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">مفقود</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- تبويب إجراءات التهيئة -->
                        <div class="tab-pane fade" id="actions" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-play text-success"></i>
                                إجراءات التهيئة والإعداد
                            </h5>

                            <?php if (!$settings_table_exists): ?>
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle"></i> جدول الإعدادات مفقود</h6>
                                <p>يجب إنشاء جدول الإعدادات أولاً قبل إضافة البيانات الافتراضية.</p>
                                <div class="mt-3">
                                    <a href="create_settings_table.php" class="btn btn-danger" target="_blank">
                                        <i class="fas fa-plus"></i> إنشاء جدول الإعدادات
                                    </a>
                                    <a href="update_database_schema.php" class="btn btn-primary" target="_blank">
                                        <i class="fas fa-database"></i> تحديث بنية قاعدة البيانات
                                    </a>
                                </div>
                            </div>
                            <?php elseif (!empty($missing_settings)): ?>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> إعدادات مفقودة</h6>
                                <p>يوجد <?php echo count($missing_settings); ?> إعداد افتراضي مفقود. سيتم إضافتها عند الضغط على الزر أدناه.</p>
                            </div>

                            <form method="POST" onsubmit="return confirm('هل أنت متأكد من إضافة جميع الإعدادات الافتراضية المفقودة؟')">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">الإعدادات التي سيتم إضافتها</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <?php
                                            $missing_by_category = [];
                                            foreach ($missing_settings as $setting) {
                                                $category = $setting[3];
                                                if (!isset($missing_by_category[$category])) {
                                                    $missing_by_category[$category] = [];
                                                }
                                                $missing_by_category[$category][] = $setting[0];
                                            }
                                            ?>

                                            <?php foreach ($missing_by_category as $category => $settings): ?>
                                            <div class="col-md-6 mb-3">
                                                <h6 class="text-primary"><?php echo $category_names[$category] ?? $category; ?></h6>
                                                <ul class="list-unstyled">
                                                    <?php foreach ($settings as $setting_key): ?>
                                                    <li><i class="fas fa-plus text-success"></i> <code><?php echo $setting_key; ?></code></li>
                                                    <?php endforeach; ?>
                                                </ul>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>

                                        <div class="text-center mt-3">
                                            <button type="submit" name="initialize_data" class="btn btn-success btn-lg">
                                                <i class="fas fa-seedling"></i> إضافة جميع الإعدادات الافتراضية
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <?php else: ?>
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle"></i> التهيئة مكتملة</h6>
                                <p>جميع الإعدادات الافتراضية موجودة. النظام جاهز للاستخدام.</p>
                            </div>

                            <div class="text-center">
                                <a href="settings.php" class="btn btn-primary">
                                    <i class="fas fa-cog"></i> إدارة الإعدادات
                                </a>
                                <a href="system_tools.php" class="btn btn-secondary">
                                    <i class="fas fa-tools"></i> أدوات النظام
                                </a>
                            </div>
                            <?php endif; ?>

                            <div class="mt-4">
                                <h6>أدوات إضافية:</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-2">
                                        <a href="update_database_schema.php" class="btn btn-outline-primary w-100" target="_blank">
                                            <i class="fas fa-database"></i> تحديث بنية قاعدة البيانات
                                        </a>
                                    </div>
                                    <div class="col-md-6 mb-2">
                                        <a href="create_settings_table.php" class="btn btn-outline-warning w-100" target="_blank">
                                            <i class="fas fa-table"></i> إنشاء جدول الإعدادات
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
