<?php
/**
 * ملف لاختبار النظام والتأكد من عمل جميع الوظائف
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

$test_results = [];

// اختبار 1: التحقق من وجود الجداول
$test_results['tables'] = [];
$required_tables = ['purchases', 'purchase_items', 'sales', 'sale_items', 'products', 'customers'];

foreach ($required_tables as $table) {
    $check_table = $db->query("SHOW TABLES LIKE '$table'");
    $test_results['tables'][$table] = ($check_table && $check_table->num_rows > 0);
}

// اختبار 2: التحقق من بنية جدول المشتريات
$test_results['purchases_structure'] = [];
if ($test_results['tables']['purchases']) {
    $required_columns = ['id', 'invoice_number', 'date', 'customer_id', 'subtotal', 'tax_amount', 'total_amount'];
    $structure = $db->query("DESCRIBE purchases");
    $existing_columns = [];
    
    while ($column = $structure->fetch_assoc()) {
        $existing_columns[] = $column['Field'];
    }
    
    foreach ($required_columns as $column) {
        $test_results['purchases_structure'][$column] = in_array($column, $existing_columns);
    }
}

// اختبار 3: التحقق من البيانات
$test_results['data'] = [];

// عدد المشتريات
if ($test_results['tables']['purchases']) {
    $count_result = $db->query("SELECT COUNT(*) as count FROM purchases");
    $test_results['data']['purchases_count'] = $count_result ? $count_result->fetch_assoc()['count'] : 0;
} else {
    $test_results['data']['purchases_count'] = 0;
}

// عدد المبيعات
if ($test_results['tables']['sales']) {
    $count_result = $db->query("SELECT COUNT(*) as count FROM sales");
    $test_results['data']['sales_count'] = $count_result ? $count_result->fetch_assoc()['count'] : 0;
} else {
    $test_results['data']['sales_count'] = 0;
}

// عدد المنتجات
if ($test_results['tables']['products']) {
    $count_result = $db->query("SELECT COUNT(*) as count FROM products");
    $test_results['data']['products_count'] = $count_result ? $count_result->fetch_assoc()['count'] : 0;
} else {
    $test_results['data']['products_count'] = 0;
}

// عدد العملاء
if ($test_results['tables']['customers']) {
    $count_result = $db->query("SELECT COUNT(*) as count FROM customers");
    $test_results['data']['customers_count'] = $count_result ? $count_result->fetch_assoc()['count'] : 0;
} else {
    $test_results['data']['customers_count'] = 0;
}

// اختبار 4: اختبار الاستعلامات المهمة
$test_results['queries'] = [];

// استعلام المشتريات اليومية
try {
    $today = date('Y-m-d');
    if ($test_results['tables']['purchases']) {
        $stmt = $db->prepare("SELECT SUM(total_amount) FROM purchases WHERE date = ?");
        $stmt->bind_param("s", $today);
        $stmt->execute();
        $stmt->bind_result($today_purchases);
        $stmt->fetch();
        $stmt->close();
        $test_results['queries']['today_purchases'] = ['success' => true, 'value' => $today_purchases ?? 0];
    } else {
        $test_results['queries']['today_purchases'] = ['success' => false, 'error' => 'جدول المشتريات غير موجود'];
    }
} catch (Exception $e) {
    $test_results['queries']['today_purchases'] = ['success' => false, 'error' => $e->getMessage()];
}

// استعلام المبيعات اليومية
try {
    if ($test_results['tables']['sales']) {
        $stmt = $db->prepare("SELECT SUM(total_amount) FROM sales WHERE date = ?");
        $stmt->bind_param("s", $today);
        $stmt->execute();
        $stmt->bind_result($today_sales);
        $stmt->fetch();
        $stmt->close();
        $test_results['queries']['today_sales'] = ['success' => true, 'value' => $today_sales ?? 0];
    } else {
        $test_results['queries']['today_sales'] = ['success' => false, 'error' => 'جدول المبيعات غير موجود'];
    }
} catch (Exception $e) {
    $test_results['queries']['today_sales'] = ['success' => false, 'error' => $e->getMessage()];
}

displayMessages();
?>

<div class="container mt-4">
    <h2>اختبار النظام</h2>
    
    <!-- اختبار الجداول -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-database"></i> اختبار وجود الجداول</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <?php foreach ($test_results['tables'] as $table => $exists): ?>
                <div class="col-md-4 mb-2">
                    <div class="d-flex align-items-center">
                        <i class="fas <?php echo $exists ? 'fa-check-circle text-success' : 'fa-times-circle text-danger'; ?> me-2"></i>
                        <span><?php echo $table; ?></span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    
    <!-- اختبار بنية جدول المشتريات -->
    <?php if ($test_results['tables']['purchases']): ?>
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-table"></i> اختبار بنية جدول المشتريات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <?php foreach ($test_results['purchases_structure'] as $column => $exists): ?>
                <div class="col-md-3 mb-2">
                    <div class="d-flex align-items-center">
                        <i class="fas <?php echo $exists ? 'fa-check-circle text-success' : 'fa-times-circle text-danger'; ?> me-2"></i>
                        <span><?php echo $column; ?></span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- اختبار البيانات -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-chart-bar"></i> اختبار البيانات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-primary"><?php echo $test_results['data']['purchases_count']; ?></h4>
                        <p>فواتير المشتريات</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-success"><?php echo $test_results['data']['sales_count']; ?></h4>
                        <p>فواتير المبيعات</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-info"><?php echo $test_results['data']['products_count']; ?></h4>
                        <p>المنتجات</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h4 class="text-warning"><?php echo $test_results['data']['customers_count']; ?></h4>
                        <p>العملاء</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- اختبار الاستعلامات -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5 class="mb-0"><i class="fas fa-search"></i> اختبار الاستعلامات</h5>
        </div>
        <div class="card-body">
            <?php foreach ($test_results['queries'] as $query_name => $result): ?>
            <div class="row mb-2">
                <div class="col-md-4">
                    <strong><?php echo $query_name; ?>:</strong>
                </div>
                <div class="col-md-8">
                    <?php if ($result['success']): ?>
                        <span class="text-success">
                            <i class="fas fa-check-circle"></i> نجح - القيمة: <?php echo number_format($result['value'], 2); ?>
                        </span>
                    <?php else: ?>
                        <span class="text-danger">
                            <i class="fas fa-times-circle"></i> فشل - <?php echo $result['error']; ?>
                        </span>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    
    <!-- الإجراءات -->
    <div class="card">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0"><i class="fas fa-tools"></i> الإجراءات المقترحة</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>إذا كانت الجداول مفقودة:</h6>
                    <a href="check_tables.php" class="btn btn-primary mb-2">
                        <i class="fas fa-database"></i> إنشاء الجداول
                    </a>
                </div>
                <div class="col-md-6">
                    <h6>إذا كانت البيانات مفقودة:</h6>
                    <a href="add_sample_data.php" class="btn btn-success mb-2">
                        <i class="fas fa-plus-circle"></i> إضافة بيانات تجريبية
                    </a>
                </div>
            </div>
            <hr>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-home"></i> العودة للصفحة الرئيسية
            </a>
            <a href="test_system.php" class="btn btn-info">
                <i class="fas fa-redo"></i> إعادة الاختبار
            </a>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
