<?php
/**
 * معالج طلبات AJAX محسن مع معالجة أفضل للأخطاء
 */

// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set('display_errors', 0); // إخفاء الأخطاء من المتصفح
ini_set('log_errors', 1); // تسجيل الأخطاء في ملف

// بدء الجلسة
session_start();

// تعيين نوع المحتوى
header('Content-Type: application/json');

// Start output buffering to prevent "headers already sent" errors
ob_start();

try {
    require_once __DIR__ . '/config/init.php';
    require_once __DIR__ . '/includes/functions.php';
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'خطأ في تحميل الملفات المطلوبة: ' . $e->getMessage()]);
    exit();
}

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit();
}

try {
    $db = getCurrentUserDB();
    if ($db === null) {
        echo json_encode(['success' => false, 'message' => 'فشل في الحصول على اتصال قاعدة البيانات']);
        exit();
    }

    if ($db->connect_error) {
        echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات: ' . $db->connect_error]);
        exit();
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'استثناء في قاعدة البيانات: ' . $e->getMessage()]);
    exit();
}

// التحقق من وجود إجراء
if (!isset($_POST['action'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'لم يتم تحديد الإجراء']);
    exit();
}

// معالجة الإجراءات المختلفة
switch ($_POST['action']) {
    case 'add_product':
        addProduct($db);
        break;
    case 'add_customer':
        addCustomer($db);
        break;
    default:
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'إجراء غير معروف']);
        exit();
}

/**
 * دالة لإضافة منتج جديد
 */
function addProduct($db) {
    // التحقق من البيانات المطلوبة
    if (empty($_POST['name'])) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'اسم المنتج مطلوب']);
        exit();
    }
    
    $name = trim($_POST['name']);
    $price = !empty($_POST['price']) ? floatval($_POST['price']) : 0;
    $tax_rate = !empty($_POST['tax_rate']) ? floatval($_POST['tax_rate']) : 0;
    
    // إدخال المنتج الجديد في قاعدة البيانات
    $stmt = $db->prepare("INSERT INTO products (name, price, tax_rate) VALUES (?, ?, ?)");
    $stmt->bind_param("sdd", $name, $price, $tax_rate);
    
    if ($stmt->execute()) {
        $product_id = $stmt->insert_id;
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'product_id' => $product_id,
            'message' => 'تم إضافة المنتج بنجاح'
        ]);
    } else {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ أثناء إضافة المنتج: ' . $db->error
        ]);
    }
    
    $stmt->close();
    exit();
}

/**
 * دالة لإضافة عميل جديد مع معالجة محسنة للأخطاء
 */
function addCustomer($db) {
    try {
        // التحقق من البيانات المطلوبة
        if (empty($_POST['name'])) {
            echo json_encode(['success' => false, 'message' => 'اسم العميل مطلوب']);
            exit();
        }

        $name = trim($_POST['name']);
        $phone = trim($_POST['phone'] ?? '');
        $tax_number = trim($_POST['tax_number'] ?? '');
        $address = trim($_POST['address'] ?? '');

        // التحقق من طول الاسم
        if (strlen($name) < 2) {
            echo json_encode(['success' => false, 'message' => 'اسم العميل يجب أن يكون أكثر من حرف واحد']);
            exit();
        }

        if (strlen($name) > 100) {
            echo json_encode(['success' => false, 'message' => 'اسم العميل طويل جداً']);
            exit();
        }

        // التحقق من وجود جدول العملاء
        $check_table = $db->query("SHOW TABLES LIKE 'customers'");
        if (!$check_table || $check_table->num_rows == 0) {
            echo json_encode(['success' => false, 'message' => 'جدول العملاء غير موجود. يرجى إنشاء الجداول أولاً.']);
            exit();
        }

        // التحقق من عدم وجود عميل بنفس الاسم
        $check_stmt = $db->prepare("SELECT id FROM customers WHERE name = ?");
        if (!$check_stmt) {
            echo json_encode(['success' => false, 'message' => 'خطأ في إعداد استعلام التحقق: ' . $db->error]);
            exit();
        }

        $check_stmt->bind_param("s", $name);
        if (!$check_stmt->execute()) {
            echo json_encode(['success' => false, 'message' => 'خطأ في تنفيذ استعلام التحقق: ' . $check_stmt->error]);
            exit();
        }

        $result = $check_stmt->get_result();
        if ($result->num_rows > 0) {
            echo json_encode(['success' => false, 'message' => 'يوجد عميل بنفس الاسم بالفعل']);
            exit();
        }
        $check_stmt->close();

        // إدراج العميل الجديد
        $stmt = $db->prepare("INSERT INTO customers (name, phone, tax_number, address) VALUES (?, ?, ?, ?)");
        if (!$stmt) {
            echo json_encode(['success' => false, 'message' => 'خطأ في إعداد استعلام الإدراج: ' . $db->error]);
            exit();
        }

        $stmt->bind_param("ssss", $name, $phone, $tax_number, $address);

        if ($stmt->execute()) {
            $customer_id = $stmt->insert_id;

            // التحقق من نجاح الإدراج
            if ($customer_id > 0) {
                echo json_encode([
                    'success' => true,
                    'customer_id' => $customer_id,
                    'message' => 'تم إضافة العميل بنجاح'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'فشل في الحصول على معرف العميل الجديد']);
            }
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'حدث خطأ أثناء إضافة العميل: ' . $stmt->error
            ]);
        }

        $stmt->close();

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'حدث استثناء أثناء إضافة العميل: ' . $e->getMessage()
        ]);
    }

    exit();
}
