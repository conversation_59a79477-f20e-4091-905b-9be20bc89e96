<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'غير مصرح لك بالوصول']);
    exit();
}

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات']);
    exit();
}

// التحقق من وجود إجراء
if (!isset($_POST['action'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'لم يتم تحديد الإجراء']);
    exit();
}

// معالجة الإجراءات المختلفة
switch ($_POST['action']) {
    case 'add_product':
        addProduct($db);
        break;
    case 'add_customer':
        addCustomer($db);
        break;
    default:
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'إجراء غير معروف']);
        exit();
}

/**
 * دالة لإضافة منتج جديد
 */
function addProduct($db) {
    // التحقق من البيانات المطلوبة
    if (empty($_POST['name'])) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'اسم المنتج مطلوب']);
        exit();
    }
    
    $name = trim($_POST['name']);
    $price = !empty($_POST['price']) ? floatval($_POST['price']) : 0;
    $tax_rate = !empty($_POST['tax_rate']) ? floatval($_POST['tax_rate']) : 0;
    
    // إدخال المنتج الجديد في قاعدة البيانات
    $stmt = $db->prepare("INSERT INTO products (name, price, tax_rate) VALUES (?, ?, ?)");
    $stmt->bind_param("sdd", $name, $price, $tax_rate);
    
    if ($stmt->execute()) {
        $product_id = $stmt->insert_id;
        
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'product_id' => $product_id,
            'message' => 'تم إضافة المنتج بنجاح'
        ]);
    } else {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ أثناء إضافة المنتج: ' . $db->error
        ]);
    }
    
    $stmt->close();
    exit();
}

/**
 * دالة لإضافة عميل جديد
 */
function addCustomer($db) {
    // التحقق من البيانات المطلوبة
    if (empty($_POST['name'])) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'اسم العميل مطلوب']);
        exit();
    }

    $name = trim($_POST['name']);
    $phone = trim($_POST['phone'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $address = trim($_POST['address'] ?? '');

    // التحقق من صحة البريد الإلكتروني إذا تم إدخاله
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'البريد الإلكتروني غير صحيح']);
        exit();
    }

    // التحقق من عدم وجود عميل بنفس الاسم
    $check_stmt = $db->prepare("SELECT id FROM customers WHERE name = ?");
    $check_stmt->bind_param("s", $name);
    $check_stmt->execute();
    $result = $check_stmt->get_result();

    if ($result->num_rows > 0) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'يوجد عميل بنفس الاسم بالفعل']);
        exit();
    }

    // إدراج العميل الجديد
    $stmt = $db->prepare("INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("ssss", $name, $phone, $email, $address);

    if ($stmt->execute()) {
        $customer_id = $stmt->insert_id;

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'customer_id' => $customer_id,
            'message' => 'تم إضافة العميل بنجاح'
        ]);
    } else {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'حدث خطأ أثناء إضافة العميل: ' . $db->error
        ]);
    }

    $stmt->close();
    exit();
}
