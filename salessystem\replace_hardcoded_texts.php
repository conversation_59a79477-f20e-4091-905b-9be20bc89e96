<?php
/**
 * أداة استبدال النصوص المكتوبة مباشرة بدالة الترجمة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// معالجة طلب الاستبدال
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['replace_texts'])) {
    $files_updated = 0;
    $replacements_made = 0;
    $errors = [];
    
    try {
        // قائمة النصوص المكتوبة مباشرة مع مفاتيح الترجمة
        $hardcoded_replacements = [
            // النصوص في index.php
            'الإعدادات' => 'settings',
            'أدوات النظام' => 'system_tools',
            'غير محدد' => 'not_specified',
            'جدول المشتريات غير موجود' => 'purchases_table_missing',
            'إنشاء الجداول' => 'create_tables',
            
            // النصوص الشائعة
            'اسم المستخدم' => 'username',
            'كلمة المرور' => 'password',
            'البريد الإلكتروني' => 'email',
            'الهاتف' => 'phone',
            'العنوان' => 'address',
            'السعر' => 'price',
            'الكمية' => 'quantity',
            'الوصف' => 'description',
            'الفئة' => 'category',
            'الباركود' => 'barcode',
            'الوحدة' => 'unit',
            'المخزون' => 'stock',
            'نشط' => 'active',
            'غير نشط' => 'inactive',
            'مفعل' => 'enabled',
            'معطل' => 'disabled',
            'نعم' => 'yes',
            'لا' => 'no',
            'موافق' => 'ok',
            'إلغاء' => 'cancel',
            'إرسال' => 'submit',
            'إعادة تعيين' => 'reset',
            'مسح' => 'clear',
            'إغلاق' => 'close',
            'فتح' => 'open',
            'جديد' => 'new',
            'قديم' => 'old',
            'الأول' => 'first',
            'الأخير' => 'last',
            'التالي' => 'next',
            'السابق' => 'previous',
            'الرئيسية' => 'home',
            'لوحة التحكم' => 'dashboard',
            'الملف الشخصي' => 'profile',
            'تسجيل الخروج' => 'logout',
            'تسجيل الدخول' => 'login',
            'تسجيل' => 'register',
            'مرحباً' => 'welcome',
            'جاري التحميل' => 'loading',
            'يرجى الانتظار' => 'please_wait',
            'نجح' => 'success',
            'خطأ' => 'error',
            'تحذير' => 'warning',
            'معلومات' => 'info',
            'ملاحظة' => 'notice',
            'مطلوب' => 'required',
            'اختياري' => 'optional',
            'متاح' => 'available',
            'غير متاح' => 'unavailable',
            'متصل' => 'online',
            'غير متصل' => 'offline',
            'منقطع' => 'disconnected',
            'جاري المعالجة' => 'processing',
            'مكتمل' => 'completed',
            'في الانتظار' => 'pending',
            'موافق عليه' => 'approved',
            'مرفوض' => 'rejected',
            'مسودة' => 'draft',
            'منشور' => 'published',
            'مؤرشف' => 'archived',
            'محذوف' => 'deleted',
            'تم الإنشاء' => 'created',
            'تم التحديث' => 'updated',
            'تم التعديل' => 'modified',
            'تم الحفظ' => 'saved',
            'تم الرفع' => 'uploaded',
            'تم التحميل' => 'downloaded',
            'تم الاستيراد' => 'imported',
            'تم التصدير' => 'exported',
            'نسخة احتياطية' => 'backup',
            'استعادة' => 'restore',
            'التكوين' => 'configuration',
            'التفضيلات' => 'preferences',
            'الخيارات' => 'options',
            'الأدوات' => 'tools',
            'المرافق' => 'utilities',
            'المساعدة' => 'help',
            'الدعم' => 'support',
            'التوثيق' => 'documentation',
            'حول' => 'about',
            'الإصدار' => 'version',
            'الترخيص' => 'license',
            'حقوق الطبع' => 'copyright',
            'جميع الحقوق محفوظة' => 'all_rights_reserved',
            
            // نصوص خاصة بالنظام
            'العملاء' => 'customers',
            'المبيعات' => 'sales',
            'المشتريات' => 'purchases',
            'المنتجات' => 'products',
            'التقارير' => 'reports',
            'حاسبة الضريبة' => 'tax_calculator',
            'إضافة عميل' => 'add_customer',
            'إضافة مبيعات' => 'add_sale',
            'إضافة مشتريات' => 'add_purchase',
            'تعديل عميل' => 'edit_customer',
            'تعديل مبيعات' => 'edit_sale',
            'تعديل مشتريات' => 'edit_purchase',
            'حذف عميل' => 'delete_customer',
            'حذف مبيعات' => 'delete_sale',
            'حذف مشتريات' => 'delete_purchase',
            'عرض' => 'view',
            'طباعة' => 'print',
            'تصدير' => 'export',
            'استيراد' => 'import',
            'بحث' => 'search',
            'تصفية' => 'filter',
            'ترتيب' => 'sort',
            'تحديث' => 'refresh',
            'إعادة تحميل' => 'reload',
            'حفظ' => 'save',
            'تراجع' => 'undo',
            'إعادة' => 'redo',
            'نسخ' => 'copy',
            'لصق' => 'paste',
            'قص' => 'cut',
            'تحديد الكل' => 'select_all',
            'إلغاء التحديد' => 'deselect_all',
            'تحديد متعدد' => 'multi_select',
            'اختيار' => 'choose',
            'تصفح' => 'browse',
            'رفع ملف' => 'upload_file',
            'تحميل ملف' => 'download_file',
            'حذف ملف' => 'delete_file',
            'إعادة تسمية' => 'rename',
            'نقل' => 'move',
            'نسخ إلى' => 'copy_to',
            'مشاركة' => 'share',
            'إرسال' => 'send',
            'استقبال' => 'receive',
            'قبول' => 'accept',
            'رفض' => 'decline',
            'تأكيد' => 'confirm',
            'إلغاء التأكيد' => 'unconfirm',
            'تفعيل' => 'activate',
            'إلغاء التفعيل' => 'deactivate',
            'تمكين' => 'enable',
            'تعطيل' => 'disable',
            'إظهار' => 'show',
            'إخفاء' => 'hide',
            'توسيع' => 'expand',
            'طي' => 'collapse',
            'تكبير' => 'maximize',
            'تصغير' => 'minimize',
            'ملء الشاشة' => 'fullscreen',
            'خروج من ملء الشاشة' => 'exit_fullscreen',
            'تقريب' => 'zoom_in',
            'تبعيد' => 'zoom_out',
            'حجم طبيعي' => 'actual_size',
            'ملائمة للشاشة' => 'fit_to_screen',
            'ملائمة للعرض' => 'fit_to_width',
            'ملائمة للارتفاع' => 'fit_to_height'
        ];
        
        // قائمة الملفات المراد تحديثها
        $files_to_update = [
            'index.php',
            'customers.php',
            'sales.php',
            'purchases.php',
            'products.php',
            'add_sale.php',
            'add_purchase.php',
            'add_customer.php',
            'edit_sale.php',
            'edit_purchase.php',
            'edit_customer.php',
            'reports.php',
            'settings.php',
            'profile.php',
            'login.php',
            'register.php',
            'includes/header.php',
            'includes/footer.php',
            'includes/sidebar.php'
        ];
        
        foreach ($files_to_update as $filename) {
            $filepath = __DIR__ . '/' . $filename;
            if (file_exists($filepath)) {
                $content = file_get_contents($filepath);
                $original_content = $content;
                $file_replacements = 0;
                
                foreach ($hardcoded_replacements as $arabic_text => $key) {
                    // عدد مرات الاستبدال في هذا الملف
                    $before_count = substr_count($content, $arabic_text);
                    
                    if ($before_count > 0) {
                        // استبدال في محتوى HTML
                        $content = preg_replace('/>' . preg_quote($arabic_text, '/') . '</', '><?php echo __(\'' . $key . '\'); ?><', $content);
                        
                        // استبدال في النصوص المقتبسة
                        $content = str_replace('"' . $arabic_text . '"', '"<?php echo __(\'' . $key . '\'); ?>"', $content);
                        $content = str_replace("'" . $arabic_text . "'", "'<?php echo __('$key'); ?>'", $content);
                        
                        // استبدال في الخصائص
                        $content = str_replace('placeholder="' . $arabic_text . '"', 'placeholder="<?php echo __(\'' . $key . '\'); ?>"', $content);
                        $content = str_replace('title="' . $arabic_text . '"', 'title="<?php echo __(\'' . $key . '\'); ?>"', $content);
                        $content = str_replace('value="' . $arabic_text . '"', 'value="<?php echo __(\'' . $key . '\'); ?>"', $content);
                        $content = str_replace('alt="' . $arabic_text . '"', 'alt="<?php echo __(\'' . $key . '\'); ?>"', $content);
                        
                        // حساب عدد الاستبدالات
                        $after_count = substr_count($content, $arabic_text);
                        $file_replacements += ($before_count - $after_count);
                    }
                }
                
                // حفظ الملف إذا تم تعديله
                if ($content !== $original_content) {
                    if (file_put_contents($filepath, $content)) {
                        $files_updated++;
                        $replacements_made += $file_replacements;
                    } else {
                        $errors[] = "فشل في حفظ ملف $filename";
                    }
                }
            }
        }
        
        if ($files_updated > 0) {
            $_SESSION['success'] = "تم تحديث $files_updated ملف وإجراء $replacements_made استبدال";
        } else {
            $_SESSION['info'] = "لا توجد نصوص تحتاج استبدال";
        }
        
        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', $errors);
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء الاستبدال: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-exchange-alt"></i>
                        استبدال النصوص المكتوبة مباشرة بدالة الترجمة
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> حول الأداة</h6>
                        <p>هذه الأداة تقوم بالبحث عن النصوص العربية المكتوبة مباشرة في الكود واستبدالها بدالة الترجمة <code>__('key')</code>.</p>
                        <ul>
                            <li>فحص شامل لجميع ملفات PHP</li>
                            <li>استبدال تلقائي للنصوص المكتوبة مباشرة</li>
                            <li>دعم جميع خصائص HTML (placeholder, title, value, alt)</li>
                            <li>حفظ تلقائي للملفات المحدثة</li>
                        </ul>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">أنواع الاستبدال</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-code text-primary"></i> محتوى HTML: <code>&gt;النص&lt;</code></li>
                                        <li><i class="fas fa-quote-right text-success"></i> النصوص المقتبسة: <code>"النص"</code></li>
                                        <li><i class="fas fa-tags text-info"></i> خصائص HTML: <code>placeholder="النص"</code></li>
                                        <li><i class="fas fa-eye text-warning"></i> عناوين: <code>title="النص"</code></li>
                                        <li><i class="fas fa-edit text-danger"></i> قيم: <code>value="النص"</code></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">الملفات المشمولة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-file-code text-primary"></i> ملفات الصفحات الرئيسية</li>
                                        <li><i class="fas fa-file-code text-success"></i> ملفات الإضافة والتعديل</li>
                                        <li><i class="fas fa-file-code text-info"></i> ملفات التقارير والإعدادات</li>
                                        <li><i class="fas fa-file-code text-warning"></i> ملفات التخطيط (header, footer)</li>
                                        <li><i class="fas fa-file-code text-danger"></i> ملفات المصادقة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم</h6>
                        <p>هذه الأداة ستقوم بتعديل ملفات PHP مباشرة. يُنصح بشدة بعمل نسخة احتياطية من المشروع قبل التطبيق.</p>
                        <ul class="mb-0">
                            <li>تأكد من وجود ملفات الترجمة قبل التطبيق</li>
                            <li>اختبر النظام بعد التطبيق للتأكد من عمله بشكل صحيح</li>
                            <li>راجع الملفات المحدثة للتأكد من صحة الاستبدال</li>
                        </ul>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6>مثال على الاستبدال:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-danger">قبل الاستبدال:</h6>
                                    <pre class="bg-light p-3"><code>&lt;button&gt;حفظ&lt;/button&gt;
&lt;input placeholder="اسم المستخدم"&gt;
&lt;h1 title="الصفحة الرئيسية"&gt;مرحباً&lt;/h1&gt;</code></pre>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success">بعد الاستبدال:</h6>
                                    <pre class="bg-light p-3"><code>&lt;button&gt;&lt;?php echo __('save'); ?&gt;&lt;/button&gt;
&lt;input placeholder="&lt;?php echo __('username'); ?&gt;"&gt;
&lt;h1 title="&lt;?php echo __('home'); ?&gt;"&gt;&lt;?php echo __('welcome'); ?&gt;&lt;/h1&gt;</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من استبدال النصوص المكتوبة مباشرة؟ يُنصح بعمل نسخة احتياطية أولاً.')">
                        <div class="text-center">
                            <button type="submit" name="replace_texts" class="btn btn-warning btn-lg">
                                <i class="fas fa-exchange-alt"></i> استبدال النصوص المكتوبة مباشرة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
