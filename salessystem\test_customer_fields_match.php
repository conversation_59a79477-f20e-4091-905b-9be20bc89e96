<?php
/**
 * ملف لاختبار تطابق حقول النافذة المنبثقة مع صفحة العملاء
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// فحص الحقول في صفحة إضافة العميل
$add_customer_fields = [];
$add_customer_file = __DIR__ . '/add_customer.php';
if (file_exists($add_customer_file)) {
    $content = file_get_contents($add_customer_file);
    
    // استخراج الحقول من النموذج
    preg_match_all('/name="([^"]+)"/', $content, $matches);
    $add_customer_fields = array_unique($matches[1]);
    
    // استخراج التسميات
    preg_match_all('/<label[^>]*for="([^"]+)"[^>]*>([^<]+)<\/label>/', $content, $label_matches);
    $add_customer_labels = [];
    for ($i = 0; $i < count($label_matches[1]); $i++) {
        $add_customer_labels[$label_matches[1][$i]] = trim($label_matches[2][$i]);
    }
}

// فحص الحقول في النافذة المنبثقة لصفحة المشتريات
$purchase_modal_fields = [];
$purchase_modal_labels = [];
$purchase_file = __DIR__ . '/add_purchase.php';
if (file_exists($purchase_file)) {
    $content = file_get_contents($purchase_file);
    
    // استخراج الحقول من النافذة المنبثقة
    preg_match_all('/id="new_customer_([^"]+)"/', $content, $matches);
    $purchase_modal_fields = array_unique($matches[1]);
    
    // استخراج التسميات
    preg_match_all('/<label[^>]*for="new_customer_([^"]+)"[^>]*>([^<]+)<\/label>/', $content, $label_matches);
    for ($i = 0; $i < count($label_matches[1]); $i++) {
        $purchase_modal_labels[$label_matches[1][$i]] = trim($label_matches[2][$i]);
    }
}

// فحص الحقول في النافذة المنبثقة لصفحة المبيعات
$sale_modal_fields = [];
$sale_modal_labels = [];
$sale_file = __DIR__ . '/add_sale.php';
if (file_exists($sale_file)) {
    $content = file_get_contents($sale_file);
    
    // استخراج الحقول من النافذة المنبثقة
    preg_match_all('/id="new_customer_([^"]+)"/', $content, $matches);
    $sale_modal_fields = array_unique($matches[1]);
    
    // استخراج التسميات
    preg_match_all('/<label[^>]*for="new_customer_([^"]+)"[^>]*>([^<]+)<\/label>/', $content, $label_matches);
    for ($i = 0; $i < count($label_matches[1]); $i++) {
        $sale_modal_labels[$label_matches[1][$i]] = trim($label_matches[2][$i]);
    }
}

// فحص بنية جدول العملاء
$db_fields = [];
$db = getCurrentUserDB();
if ($db && !$db->connect_error) {
    $check_table = $db->query("SHOW TABLES LIKE 'customers'");
    if ($check_table && $check_table->num_rows > 0) {
        $structure = $db->query("DESCRIBE customers");
        while ($row = $structure->fetch_assoc()) {
            $db_fields[] = $row['Field'];
        }
    }
}

displayMessages();
?>

<div class="container mt-4">
    <h2>اختبار تطابق حقول العملاء</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات الاختبار</h5>
        <p>هذا الاختبار يتحقق من تطابق الحقول بين:</p>
        <ul>
            <li>صفحة إضافة العميل الرئيسية</li>
            <li>النافذة المنبثقة في صفحة المشتريات</li>
            <li>النافذة المنبثقة في صفحة المبيعات</li>
            <li>بنية جدول العملاء في قاعدة البيانات</li>
        </ul>
    </div>
    
    <div class="row">
        <!-- صفحة إضافة العميل -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user-plus"></i>
                        صفحة إضافة العميل
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($add_customer_fields)): ?>
                        <h6>الحقول الموجودة:</h6>
                        <ul class="list-group">
                            <?php foreach ($add_customer_fields as $field): ?>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>
                                    <strong><?php echo $field; ?></strong>
                                    <?php if (isset($add_customer_labels[$field])): ?>
                                        <br><small class="text-muted"><?php echo $add_customer_labels[$field]; ?></small>
                                    <?php endif; ?>
                                </span>
                                <span class="badge bg-primary">✓</span>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php else: ?>
                        <div class="alert alert-warning">لم يتم العثور على حقول</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- جدول قاعدة البيانات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-database"></i>
                        جدول قاعدة البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($db_fields)): ?>
                        <h6>أعمدة الجدول:</h6>
                        <ul class="list-group">
                            <?php foreach ($db_fields as $field): ?>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <strong><?php echo $field; ?></strong>
                                <span class="badge bg-success">✓</span>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php else: ?>
                        <div class="alert alert-danger">
                            جدول العملاء غير موجود أو لا يمكن الوصول إليه
                            <br>
                            <a href="fix_customer_table.php" class="btn btn-sm btn-primary mt-2">إصلاح الجدول</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- نافذة المشتريات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-shopping-cart"></i>
                        نافذة المشتريات
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($purchase_modal_fields)): ?>
                        <h6>الحقول الموجودة:</h6>
                        <ul class="list-group">
                            <?php foreach ($purchase_modal_fields as $field): ?>
                            <?php 
                            $is_matching = in_array($field, $add_customer_fields);
                            $badge_class = $is_matching ? 'bg-success' : 'bg-warning';
                            $icon = $is_matching ? '✓' : '⚠';
                            ?>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>
                                    <strong><?php echo $field; ?></strong>
                                    <?php if (isset($purchase_modal_labels[$field])): ?>
                                        <br><small class="text-muted"><?php echo $purchase_modal_labels[$field]; ?></small>
                                    <?php endif; ?>
                                </span>
                                <span class="badge <?php echo $badge_class; ?>"><?php echo $icon; ?></span>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php else: ?>
                        <div class="alert alert-warning">لم يتم العثور على حقول</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- نافذة المبيعات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-cash-register"></i>
                        نافذة المبيعات
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($sale_modal_fields)): ?>
                        <h6>الحقول الموجودة:</h6>
                        <ul class="list-group">
                            <?php foreach ($sale_modal_fields as $field): ?>
                            <?php 
                            $is_matching = in_array($field, $add_customer_fields);
                            $badge_class = $is_matching ? 'bg-success' : 'bg-warning';
                            $icon = $is_matching ? '✓' : '⚠';
                            ?>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>
                                    <strong><?php echo $field; ?></strong>
                                    <?php if (isset($sale_modal_labels[$field])): ?>
                                        <br><small class="text-muted"><?php echo $sale_modal_labels[$field]; ?></small>
                                    <?php endif; ?>
                                </span>
                                <span class="badge <?php echo $badge_class; ?>"><?php echo $icon; ?></span>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php else: ?>
                        <div class="alert alert-warning">لم يتم العثور على حقول</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- تحليل التطابق -->
    <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">
                <i class="fas fa-chart-bar"></i>
                تحليل التطابق
            </h5>
        </div>
        <div class="card-body">
            <?php
            $all_fields = array_unique(array_merge($add_customer_fields, $purchase_modal_fields, $sale_modal_fields));
            $matching_count = 0;
            $total_comparisons = 0;
            ?>
            
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>الحقل</th>
                            <th>صفحة العميل</th>
                            <th>نافذة المشتريات</th>
                            <th>نافذة المبيعات</th>
                            <th>جدول قاعدة البيانات</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($all_fields as $field): ?>
                        <?php
                        $in_add_customer = in_array($field, $add_customer_fields);
                        $in_purchase_modal = in_array($field, $purchase_modal_fields);
                        $in_sale_modal = in_array($field, $sale_modal_fields);
                        $in_db = in_array($field, $db_fields);
                        
                        $matches = [$in_add_customer, $in_purchase_modal, $in_sale_modal, $in_db];
                        $match_count = array_sum($matches);
                        $total_comparisons++;
                        
                        if ($match_count >= 3) $matching_count++;
                        
                        $row_class = '';
                        if ($match_count == 4) $row_class = 'table-success';
                        elseif ($match_count >= 2) $row_class = 'table-warning';
                        else $row_class = 'table-danger';
                        ?>
                        <tr class="<?php echo $row_class; ?>">
                            <td><strong><?php echo $field; ?></strong></td>
                            <td><?php echo $in_add_customer ? '✓' : '✗'; ?></td>
                            <td><?php echo $in_purchase_modal ? '✓' : '✗'; ?></td>
                            <td><?php echo $in_sale_modal ? '✓' : '✗'; ?></td>
                            <td><?php echo $in_db ? '✓' : '✗'; ?></td>
                            <td>
                                <?php if ($match_count == 4): ?>
                                    <span class="badge bg-success">مطابق تماماً</span>
                                <?php elseif ($match_count >= 2): ?>
                                    <span class="badge bg-warning">مطابق جزئياً</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير مطابق</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <?php
            $match_percentage = $total_comparisons > 0 ? ($matching_count / $total_comparisons) * 100 : 0;
            ?>
            
            <div class="mt-3">
                <h6>نسبة التطابق الإجمالية:</h6>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar <?php echo $match_percentage >= 80 ? 'bg-success' : ($match_percentage >= 60 ? 'bg-warning' : 'bg-danger'); ?>" 
                         style="width: <?php echo $match_percentage; ?>%">
                        <?php echo number_format($match_percentage, 1); ?>%
                    </div>
                </div>
                <small class="text-muted">
                    <?php echo $matching_count; ?> من <?php echo $total_comparisons; ?> حقول متطابقة
                </small>
            </div>
        </div>
    </div>
    
    <!-- الإجراءات -->
    <div class="card">
        <div class="card-header bg-dark text-white">
            <h5 class="mb-0">الإجراءات والأدوات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <h6>اختبار الميزة:</h6>
                    <div class="d-grid gap-2">
                        <a href="add_purchase.php" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-shopping-cart"></i> اختبار المشتريات
                        </a>
                        <a href="add_sale.php" class="btn btn-outline-success btn-sm" target="_blank">
                            <i class="fas fa-cash-register"></i> اختبار المبيعات
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>إدارة العملاء:</h6>
                    <div class="d-grid gap-2">
                        <a href="add_customer.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-user-plus"></i> إضافة عميل
                        </a>
                        <a href="customers.php" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-users"></i> قائمة العملاء
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>إصلاح وتشخيص:</h6>
                    <div class="d-grid gap-2">
                        <a href="fix_customer_table.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-tools"></i> إصلاح الجدول
                        </a>
                        <a href="test_ajax_customer.php" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-bug"></i> اختبار AJAX
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>العودة:</h6>
                    <div class="d-grid gap-2">
                        <a href="index.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <a href="test_customer_fields_match.php" class="btn btn-info btn-sm">
                            <i class="fas fa-redo"></i> إعادة الاختبار
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
