<?php
/**
 * اختبار الترجمات المحدثة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// قائمة الترجمات الجديدة للاختبار
$new_translations = [
    'at_least_one_item_required',
    'sale_invoice_added_successfully',
    'error_adding_sale_invoice',
    'add_sales_invoice',
    'select_customer',
    'add_new_customer_option',
    'save_invoice',
    'select_product',
    'add_new_product_option',
    'product_added_successfully',
    'error_adding_product',
    'please_enter_customer_name',
    'customer_name_min_length',
    'customer_added_successfully',
    'error_adding_customer',
    'network_error_adding_customer',
    'purchase_invoice_added_successfully',
    'error_adding_purchase_invoice',
    'add_purchase_invoice',
    'supplier',
    'select_supplier',
    'add_new_supplier_option',
    'add_new_customer',
    'customer_saved_successfully',
    'error_saving_customer',
    'customers_list',
    'no_customers_found',
    'sales_list',
    'no_sales_found',
    'purchases_list',
    'no_purchases_found',
    'product',
    'phone',
    'enabled',
    'disabled',
    'ok',
    'next',
    'previous',
    'first',
    'last',
    'active',
    'inactive',
    'new',
    'old',
    'import',
    'export',
    'backup',
    'restore',
    'home_page'
];

// تحميل ملفات الترجمة
$ar_translations = require __DIR__ . '/languages/ar/lang.php';
$en_translations = require __DIR__ . '/languages/en/lang.php';

// فحص الترجمات
$missing_ar = [];
$missing_en = [];
$working_ar = [];
$working_en = [];

foreach ($new_translations as $key) {
    if (isset($ar_translations[$key])) {
        $working_ar[] = $key;
    } else {
        $missing_ar[] = $key;
    }
    
    if (isset($en_translations[$key])) {
        $working_en[] = $key;
    } else {
        $missing_en[] = $key;
    }
}

$total_new = count($new_translations);
$ar_success_rate = round((count($working_ar) / $total_new) * 100, 1);
$en_success_rate = round((count($working_en) / $total_new) * 100, 1);

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-check-circle"></i>
                        اختبار الترجمات المحدثة
                    </h4>
                </div>
                <div class="card-body">
                    <!-- إحصائيات عامة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $total_new; ?></h3>
                                    <p class="mb-0">ترجمات جديدة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($working_ar); ?></h3>
                                    <p class="mb-0">عربية جاهزة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($working_en); ?></h3>
                                    <p class="mb-0">إنجليزية جاهزة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h3><?php echo (count($missing_ar) + count($missing_en)); ?></h3>
                                    <p class="mb-0">مفقودة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معدل النجاح -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">معدل نجاح الترجمة العربية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="progress mb-3" style="height: 30px;">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo $ar_success_rate; ?>%">
                                            <?php echo $ar_success_rate; ?>%
                                        </div>
                                    </div>
                                    <p><strong>جاهزة:</strong> <?php echo count($working_ar); ?> من <?php echo $total_new; ?></p>
                                    <p><strong>مفقودة:</strong> <?php echo count($missing_ar); ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">معدل نجاح الترجمة الإنجليزية</h6>
                                </div>
                                <div class="card-body">
                                    <div class="progress mb-3" style="height: 30px;">
                                        <div class="progress-bar bg-info" role="progressbar" style="width: <?php echo $en_success_rate; ?>%">
                                            <?php echo $en_success_rate; ?>%
                                        </div>
                                    </div>
                                    <p><strong>جاهزة:</strong> <?php echo count($working_en); ?> من <?php echo $total_new; ?></p>
                                    <p><strong>مفقودة:</strong> <?php echo count($missing_en); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- اختبار عملي للترجمات -->
                    <h5 class="mb-3">
                        <i class="fas fa-vial text-primary"></i>
                        اختبار عملي للترجمات
                    </h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">عينة من الترجمات العربية</h6>
                                </div>
                                <div class="card-body">
                                    <?php foreach (array_slice($working_ar, 0, 10) as $key): ?>
                                    <div class="mb-2">
                                        <code><?php echo $key; ?></code><br>
                                        <span class="text-success"><?php echo __($key); ?></span>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">عينة من الترجمات الإنجليزية</h6>
                                </div>
                                <div class="card-body">
                                    <?php 
                                    // تغيير اللغة مؤقتاً للإنجليزية
                                    $current_lang = $_SESSION['language'] ?? 'ar';
                                    $_SESSION['language'] = 'en';
                                    ?>
                                    <?php foreach (array_slice($working_en, 0, 10) as $key): ?>
                                    <div class="mb-2">
                                        <code><?php echo $key; ?></code><br>
                                        <span class="text-info"><?php echo __($key); ?></span>
                                    </div>
                                    <?php endforeach; ?>
                                    <?php 
                                    // إعادة اللغة الأصلية
                                    $_SESSION['language'] = $current_lang;
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الترجمات المفقودة -->
                    <?php if (!empty($missing_ar) || !empty($missing_en)): ?>
                    <div class="mt-4">
                        <h5 class="mb-3">
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                            الترجمات المفقودة
                        </h5>
                        
                        <div class="row">
                            <?php if (!empty($missing_ar)): ?>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">مفقودة بالعربية (<?php echo count($missing_ar); ?>)</h6>
                                    </div>
                                    <div class="card-body">
                                        <?php foreach ($missing_ar as $key): ?>
                                        <div class="text-danger">• <?php echo $key; ?></div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($missing_en)): ?>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-danger text-white">
                                        <h6 class="mb-0">مفقودة بالإنجليزية (<?php echo count($missing_en); ?>)</h6>
                                    </div>
                                    <div class="card-body">
                                        <?php foreach ($missing_en as $key): ?>
                                        <div class="text-danger">• <?php echo $key; ?></div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- النتيجة النهائية -->
                    <div class="mt-4">
                        <?php if (empty($missing_ar) && empty($missing_en)): ?>
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle"></i> ممتاز!</h5>
                            <p>جميع الترجمات الجديدة تعمل بشكل صحيح. النظام جاهز للاستخدام.</p>
                        </div>
                        <?php elseif ($ar_success_rate >= 90 && $en_success_rate >= 90): ?>
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle"></i> جيد جداً</h5>
                            <p>معظم الترجمات تعمل بشكل صحيح. يُنصح بإصلاح الترجمات المفقودة.</p>
                        </div>
                        <?php else: ?>
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-times-circle"></i> يحتاج إصلاح</h5>
                            <p>يوجد عدد كبير من الترجمات المفقودة. يجب إصلاحها قبل الاستخدام.</p>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mt-3">
                            <a href="review_translations.php" class="btn btn-primary">
                                <i class="fas fa-language"></i> مراجعة شاملة للترجمات
                            </a>
                            <a href="fix_hardcoded_texts.php" class="btn btn-warning">
                                <i class="fas fa-code"></i> إصلاح النصوص المكتوبة مباشرة
                            </a>
                            <a href="system_tools.php" class="btn btn-secondary">
                                <i class="fas fa-tools"></i> أدوات النظام
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
